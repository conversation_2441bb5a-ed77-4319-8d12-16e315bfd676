<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Financial Allocation Chart</title>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f9fafb;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .chart-title {
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 24px;
            text-align: center;
        }
        
        #chart {
            width: 100%;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2 class="chart-title">Financial Allocation Overview</h2>
        <div id="chart"></div>
    </div>

    <script>
        const options = {
            series: [
                {
                    name: 'Allocation',
                    data: [27, 0, 0, 0, 0]
                },
                {
                    name: 'Financial Allocation', 
                    data: [0, 30, 0, 0, 0]
                },
                {
                    name: 'Q1',
                    data: [0, 0, 3, 0, 0]
                },
                {
                    name: 'Q2',
                    data: [0, 0, 4, 0, 0]
                },
                {
                    name: 'Q3',
                    data: [0, 0, 4, 0, 0]
                },
                {
                    name: 'Q4',
                    data: [0, 0, 4, 0, 0]
                },
                {
                    name: 'Surrender',
                    data: [0, 0, 0, 30, 0]
                },
                {
                    name: 'Expenditure',
                    data: [0, 0, 0, 0, 5]
                }
            ],
            chart: {
                type: 'bar',
                height: 400,
                stacked: true,
                toolbar: {
                    show: false
                },
                background: 'transparent'
            },
            colors: [
                '#8B5CF6', // Purple for Allocation
                '#10B981', // Green for Financial Allocation  
                '#F59E0B', // Orange for Q1
                '#F97316', // Orange for Q2
                '#EA580C', // Orange for Q3
                '#DC2626', // Red-Orange for Q4
                '#3B82F6', // Blue for Surrender
                '#EF4444'  // Red for Expenditure
            ],
            plotOptions: {
                bar: {
                    horizontal: false,
                    columnWidth: '60%',
                    borderRadius: 8,
                    borderRadiusApplication: 'end'
                }
            },
            dataLabels: {
                enabled: false
            },
            stroke: {
                show: false
            },
            xaxis: {
                categories: ['Allocation', 'Financial Allocation', 'Releases', 'Surrender', 'Expenditure'],
                labels: {
                    style: {
                        fontSize: '12px',
                        fontWeight: 500,
                        colors: '#374151'
                    }
                },
                axisBorder: {
                    show: false
                },
                axisTicks: {
                    show: false
                }
            },
            yaxis: {
                labels: {
                    formatter: function (val) {
                        if (val >= 1000000) {
                            return (val / 1000000).toFixed(0) + 'M';
                        }
                        return val;
                    },
                    style: {
                        fontSize: '12px',
                        colors: '#6B7280'
                    }
                },
                min: 0,
                max: 50,
                tickAmount: 5
            },
            grid: {
                show: true,
                borderColor: '#E5E7EB',
                strokeDashArray: 3,
                xaxis: {
                    lines: {
                        show: false
                    }
                },
                yaxis: {
                    lines: {
                        show: true
                    }
                }
            },
            legend: {
                show: true,
                position: 'bottom',
                horizontalAlign: 'center',
                fontSize: '12px',
                fontWeight: 500,
                labels: {
                    colors: '#374151'
                },
                markers: {
                    width: 12,
                    height: 12,
                    radius: 2
                },
                itemMargin: {
                    horizontal: 15,
                    vertical: 5
                },
                formatter: function(seriesName, opts) {
                    // Group Q1-Q4 under "Releases"
                    if (['Q1', 'Q2', 'Q3', 'Q4'].includes(seriesName)) {
                        return 'Releases';
                    }
                    return seriesName;
                }
            },
            tooltip: {
                shared: true,
                intersect: false,
                y: {
                    formatter: function (val, opts) {
                        if (val > 0) {
                            return val + 'M';
                        }
                        return '';
                    }
                },
                custom: function({series, seriesIndex, dataPointIndex, w}) {
                    // Custom tooltip for Releases column to show 78%
                    if (dataPointIndex === 2) { // Releases column
                        let total = 0;
                        for (let i = 2; i <= 5; i++) { // Q1-Q4 series
                            total += series[i][dataPointIndex];
                        }
                        return `
                            <div style="padding: 10px; background: white; border-radius: 6px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                                <div style="font-weight: bold; margin-bottom: 5px;">Releases</div>
                                <div>Total: ${total}M</div>
                                <div style="color: #F59E0B; font-weight: bold;">78% Released</div>
                            </div>
                        `;
                    }
                    return null;
                }
            },
            annotations: {
                points: [
                    {
                        x: 'Releases',
                        y: 18,
                        marker: {
                            size: 0
                        },
                        label: {
                            text: '78%',
                            style: {
                                background: '#F59E0B',
                                color: '#FFFFFF',
                                fontSize: '11px',
                                fontWeight: 'bold',
                                padding: {
                                    left: 8,
                                    right: 8,
                                    top: 4,
                                    bottom: 4
                                }
                            },
                            offsetY: -10
                        }
                    }
                ]
            }
        };

        const chart = new ApexCharts(document.querySelector("#chart"), options);
        chart.render();
    </script>
</body>
</html>


