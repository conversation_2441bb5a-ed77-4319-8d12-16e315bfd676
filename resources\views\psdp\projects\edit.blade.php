@extends('layouts.app')

@section('title', 'Edit Project')

@section('content')
<div class="bg-[#ebf3f8] min-h-screen">
    <div class="flex justify-between items-center p-5">
        <h2 class="text-[24px] text-[#0D163A] font-semibold">Edit Project</h2>
        <a href="{{ route('psdp.projects.show', $project) }}" 
           class="bg-gray-200 text-gray-700 px-6 py-3 rounded-full font-medium hover:bg-gray-300">
            Back to Project
        </a>
    </div>

    <div class="p-5">
        <div class="w-full bg-white rounded-md p-8">
            <form method="POST" action="{{ route('psdp.projects.update', $project) }}">
                @csrf
                @method('PUT')

                <!-- Project Basic Information -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Project Information</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Project Name *</label>
                            <input type="text" name="name" id="name" value="{{ old('name', $project->name) }}" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-full focus:ring-2 focus:ring-[#1A92AA] focus:border-transparent">
                            @error('name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="department_id" class="block text-sm font-medium text-gray-700 mb-2">Department *</label>
                            <select name="department_id" id="department_id" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-full focus:ring-2 focus:ring-[#1A92AA] focus:border-transparent">
                                <option value="">Select Department</option>
                                @foreach($departments as $department)
                                    <option value="{{ $department->id }}" {{ old('department_id', $project->department_id) == $department->id ? 'selected' : '' }}>
                                        {{ $department->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('department_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="psdp_no" class="block text-sm font-medium text-gray-700 mb-2">PSDP No *</label>
                            <input type="text" name="psdp_no" id="psdp_no" value="{{ old('psdp_no', $project->psdp_no) }}" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-full focus:ring-2 focus:ring-[#1A92AA] focus:border-transparent">
                            @error('psdp_no')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="psdp_id" class="block text-sm font-medium text-gray-700 mb-2">PSDP ID *</label>
                            <input type="text" name="psdp_id" id="psdp_id" value="{{ old('psdp_id', $project->psdp_id) }}" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-full focus:ring-2 focus:ring-[#1A92AA] focus:border-transparent">
                            @error('psdp_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="approving_authority_id" class="block text-sm font-medium text-gray-700 mb-2">Approving Authority *</label>
                            <select name="approving_authority_id" id="approving_authority_id" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-full focus:ring-2 focus:ring-[#1A92AA] focus:border-transparent">
                                <option value="">Select Approving Authority</option>
                                @foreach($approvingAuthorities as $authority)
                                    <option value="{{ $authority->id }}" {{ old('approving_authority_id', $project->approving_authority_id) == $authority->id ? 'selected' : '' }}>
                                        {{ $authority->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('approving_authority_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Dates Section -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Project Dates</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="authority_approved_date" class="block text-sm font-medium text-gray-700 mb-2">Authority Approved Date</label>
                            <input type="date" name="authority_approved_date" id="authority_approved_date" 
                                   value="{{ old('authority_approved_date', $project->authority_approved_date?->format('Y-m-d')) }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-full focus:ring-2 focus:ring-[#1A92AA] focus:border-transparent">
                            @error('authority_approved_date')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="admin_approval_date" class="block text-sm font-medium text-gray-700 mb-2">Project Start Date</label>
                            <input type="date" name="admin_approval_date" id="admin_approval_date" 
                                   value="{{ old('admin_approval_date', $project->admin_approval_date?->format('Y-m-d')) }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-full focus:ring-2 focus:ring-[#1A92AA] focus:border-transparent">
                            @error('admin_approval_date')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="completion_date_pc1" class="block text-sm font-medium text-gray-700 mb-2">Project End Date</label>
                            <input type="date" name="completion_date_pc1" id="completion_date_pc1" 
                                   value="{{ old('completion_date_pc1', $project->completion_date_pc1?->format('Y-m-d')) }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-full focus:ring-2 focus:ring-[#1A92AA] focus:border-transparent">
                            @error('completion_date_pc1')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="likely_completion_date" class="block text-sm font-medium text-gray-700 mb-2">Likely Completion Date</label>
                            <input type="date" name="likely_completion_date" id="likely_completion_date" 
                                   value="{{ old('likely_completion_date', $project->likely_completion_date?->format('Y-m-d')) }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-full focus:ring-2 focus:ring-[#1A92AA] focus:border-transparent">
                            @error('likely_completion_date')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Financial Information -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Financial Information</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label for="total_cost" class="block text-sm font-medium text-gray-700 mb-2">Total Cost (PKR)</label>
                            <input type="number" name="total_cost" id="total_cost" step="0.01" min="0"
                                   value="{{ old('total_cost', $project->total_cost) }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-full focus:ring-2 focus:ring-[#1A92AA] focus:border-transparent">
                            @error('total_cost')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="lc_amount" class="block text-sm font-medium text-gray-700 mb-2">LC Amount (PKR)</label>
                            <input type="number" name="lc_amount" id="lc_amount" step="0.01" min="0"
                                   value="{{ old('lc_amount', $project->lc_amount) }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-full focus:ring-2 focus:ring-[#1A92AA] focus:border-transparent">
                            @error('lc_amount')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="fc_amount" class="block text-sm font-medium text-gray-700 mb-2">FC Amount (PKR)</label>
                            <input type="number" name="fc_amount" id="fc_amount" step="0.01" min="0"
                                   value="{{ old('fc_amount', $project->fc_amount) }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-full focus:ring-2 focus:ring-[#1A92AA] focus:border-transparent">
                            @error('fc_amount')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="flex justify-center gap-4">
                    <a href="{{ route('psdp.projects.show', $project) }}" 
                       class="px-8 py-3 bg-gray-200 text-gray-700 rounded-full font-medium hover:bg-gray-300">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="px-8 py-3 bg-[#1A92AA] text-white rounded-full font-medium hover:brightness-110">
                        Update Project
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
