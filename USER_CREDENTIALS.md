# PSDP Dashboard - User Credentials

## Default User Accounts

The system has been seeded with 3 main user accounts for testing different roles and permissions:

### Super Admin User
| Name | Email | Password | Role | Permissions |
|------|-------|----------|------|-------------|
| Super Admin | <EMAIL> | password | super admin | All permissions |

### Admin User
| Name | Email | Password | Role | Permissions |
|------|-------|----------|------|-------------|
| Admin User | <EMAIL> | password | admin | Project management, reporting |

### Project Director (PD) User
| Name | Email | Password | Role | Permissions |
|------|-------|----------|------|-------------|
| Project Director | <EMAIL> | password | PD | Limited project access |

## Role Permissions

### Super Admin
- All system permissions
- User management
- Role management
- System settings
- Full project access
- Export data

### Admin
- View projects
- Create projects
- Edit projects
- Delete projects
- View reports
- Export data

### PD (Project Director)
- View projects
- Create projects
- Edit projects
- View reports

## Testing Different Roles

To test the different permission levels:

1. **Login as Super Admin** (<EMAIL>) to access all features
2. **Login as Admin** (<EMAIL>) to test project management capabilities
3. **Login as PD** (<EMAIL>) to test limited project access

## Security Notes

- All users have the same default password: `password`
- In production, ensure all users change their passwords
- Consider implementing password complexity requirements
- Enable two-factor authentication for admin users

## Quick Login Links

For easy testing, you can use these credentials:

- **Super Admin**: <EMAIL> / password
- **Admin**: <EMAIL> / password
- **PD**: <EMAIL> / password
