<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PSDP\Status;

class StatusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $statuses = [
            ['id' => 1, 'name' => 'Completed'],
            ['id' => 2, 'name' => 'Delayed'],
            ['id' => 3, 'name' => 'Not Started'],
            ['id' => 4, 'name' => 'Behind'],
            ['id' => 5, 'name' => 'On-track'],
        ];

        foreach ($statuses as $status) {
            Status::updateOrCreate(
                ['id' => $status['id']],
                $status
            );
        }
    }
}
