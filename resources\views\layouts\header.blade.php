<nav class="w-full bg-transparent p-5">
    <div class="flex flex-col lg:flex-row justify-between items-center gap-4">
        <div class="flex gap-6 lg:gap-10 items-center">
            <div class="flex gap-3 items-center">
                <img src="{{ asset('assets/moitt-logo.svg') }}" alt="" class="w-8 h-8">
                <p class="font-semibold text-[16px] lg:text-[17px]">Ministry of IT & Telecom</p>
            </div>
            <img src="{{ asset('assets/menu-icon.svg') }}" alt="" class="w-6 h-6 cursor-pointer hover:opacity-70 transition-opacity">
        </div>

        <div class="flex flex-col sm:flex-row gap-3 lg:gap-5 items-center">
            <!-- Financial Year Dropdown -->
            <div class="relative">
                <select id="financialYearSelect" class="cursor-pointer bg-[#1A92AA] text-white w-[240px] lg:w-[270px] h-[56px] lg:h-[66px] rounded-full px-6 font-semibold text-[18px] lg:text-[20px] border-0 focus:outline-none focus:ring-2 focus:ring-[#157a94] appearance-none text-center">
                    @if(isset($financialYears))
                        @foreach($financialYears as $fy)
                            @php
                                $isCurrentFY = $fy->name == '2025-2026';
                                $isSelected = isset($selectedFinancialYear) ? $selectedFinancialYear->id == $fy->id : $isCurrentFY;
                            @endphp
                            <option value="{{ $fy->id }}" {{ $isSelected ? 'selected' : '' }}>
                                FY-{{ $fy->name }}
                            </option>
                        @endforeach
                    @else
                        <option value="16" selected>FY-2025-26</option>
                    @endif
                </select>
                <div class="pointer-events-none absolute inset-y-0 right-4 flex items-center text-white">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </div>
            </div>

            <!-- User Section -->
            <div class="cursor-pointer w-[240px] lg:w-[270px] h-[56px] lg:h-[66px] relative flex justify-center items-center bg-white rounded-full shadow-sm hover:shadow-md transition-shadow">
                <!-- Notification Bell -->
                <div class="relative left-2 cursor-pointer w-[44px] lg:w-[50px] h-[44px] lg:h-[50px] rounded-full border border-[#DEDEDE] bg-white flex justify-center items-center hover:bg-gray-50 transition-colors">
                    <div class="relative">
                        <img src="{{ asset('assets/notification.png') }}" alt="" class="w-5 h-5">
                        <!-- Notification badge -->
                        <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">0</span>
                    </div>
                </div>

                <!-- User Dropdown -->
                <div id="dropdownUserButton" data-dropdown-toggle="dropdownUserMenu" class="flex items-center gap-2 hover:bg-gray-50 rounded-full px-2 py-1 transition-colors">
                    <img src="{{ asset('assets/user-icon.svg') }}" class="w-[36px] lg:w-[40px] h-[36px] lg:h-[40px]">
                    <div class="flex flex-col items-start">
                        <p class="font-semibold text-[14px] lg:text-[16px] text-gray-800">{{ auth()->user()->name ?? 'User' }}</p>
                        <p class="text-[12px] text-gray-500">
                            @if(auth()->user()->hasRole('super admin'))
                                Super Admin
                            @elseif(auth()->user()->hasRole('admin'))
                                Ministry Admin
                            @elseif(auth()->user()->hasRole('PD'))
                                Project Director
                            @else
                                User
                            @endif
                        </p>
                    </div>
                    <button class="cursor-pointer flex items-center focus:outline-none ml-1">
                        <img src="{{ asset('assets/dropdown.svg') }}" alt="" class="w-4 h-4" />
                    </button>
                </div>

                <!-- Enhanced Dropdown Menu -->
                <div id="dropdownUserMenu" class="z-20 hidden bg-white divide-y divide-gray-100 rounded-lg shadow-lg w-56 absolute top-full right-0 mt-2 border">
                    <div class="px-4 py-3 bg-gray-50 rounded-t-lg">
                        <p class="text-sm font-medium text-gray-900">{{ auth()->user()->name ?? 'User' }}</p>
                        <p class="text-sm text-gray-500 truncate">{{ auth()->user()->email ?? '<EMAIL>' }}</p>
                    </div>
                    <ul class="py-2 text-sm text-gray-700">
                        <li>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors">
                                <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                Profile
                            </a>
                        </li>

                        <!-- Dashboard Links -->
                        @if(auth()->user()->hasRole('super admin'))
                        <li>
                            <a href="{{ route('psdp.dashboard') }}" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors">
                                <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                Super Admin Dashboard
                            </a>
                        </li>
                        @elseif(auth()->user()->hasRole('admin'))
                        <li>
                            <a href="{{ route('psdp.dashboard') }}" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors">
                                <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 0 01-2-2z"></path>
                                </svg>
                                Ministry Dashboard
                            </a>
                        </li>
                        @elseif(auth()->user()->hasRole('PD'))
                        <li>
                            <a href="{{ route('psdp.pd-dashboard') }}" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors">
                                <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 0 01-2-2z"></path>
                                </svg>
                                PD Dashboard
                            </a>
                        </li>
                        @endif

                        <!-- PD Dashboard and Projects - Available to All Users -->
                        <li>
                            <a href="{{ route('psdp.pd-dashboard') }}" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors">
                                <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 0 01-2-2z"></path>
                                </svg>
                                Project Dashboard
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('psdp.pd-projects.index') }}" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors">
                                <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                                Project Management
                            </a>
                        </li>

                        <!-- User Management Links (Role-based) -->
                        @if(auth()->user()->hasRole('super admin'))
                        <li>
                            <a href="{{ route('users.index') }}" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors">
                                <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                </svg>
                                Manage Ministry Users
                            </a>
                        </li>
                        @elseif(auth()->user()->hasRole('admin'))
                        <li>
                            <a href="{{ route('users.index') }}" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors">
                                <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                </svg>
                                Manage PD Users
                            </a>
                        </li>
                        @endif

                        <li>
                            <a href="#" class="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors">
                                <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                Settings
                            </a>
                        </li>
                    </ul>
                    <div class="py-2">
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit" class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                </svg>
                                Logout
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</nav>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const dropdownButton = document.getElementById('dropdownUserButton');
    const dropdownMenu = document.getElementById('dropdownUserMenu');
    const financialYearSelect = document.getElementById('financialYearSelect');

    // User dropdown functionality
    dropdownButton.addEventListener('click', function(e) {
        e.stopPropagation();
        dropdownMenu.classList.toggle('hidden');
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
        if (!dropdownButton.contains(event.target) && !dropdownMenu.contains(event.target)) {
            dropdownMenu.classList.add('hidden');
        }
    });

    // Financial Year dropdown functionality
    if (financialYearSelect) {
        financialYearSelect.addEventListener('change', function() {
            const selectedFY = this.value;
            const currentUrl = new URL(window.location.href);
            currentUrl.searchParams.set('financial_year_id', selectedFY);

            // Add loading state
            this.style.opacity = '0.7';
            this.disabled = true;

            // Redirect with new financial year
            window.location.href = currentUrl.toString();
        });
    }

    // Add smooth transitions
    dropdownMenu.style.transition = 'opacity 0.2s ease-in-out, transform 0.2s ease-in-out';

    // Notification bell click handler
    const notificationBell = document.querySelector('[src*="notification.png"]').parentElement;
    notificationBell.addEventListener('click', function(e) {
        e.stopPropagation();
        // Add notification panel functionality here
        console.log('Notifications clicked');
    });
});
</script>
