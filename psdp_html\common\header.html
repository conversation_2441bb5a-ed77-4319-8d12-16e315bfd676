<nav class="w-full bg-transparent p-5">

    <div class="flex flex-col md:flex-row justify-between items-center">
        <div class="flex gap-10 items-center">
            <div class="flex gap-3 items-center">
                <img src="./assets/moitt-logo.svg" alt="">
                <p class="font-semibold text-[17px]">Ministry of IT & Telecom</p>
            </div>
            <img src="./assets/menu-icon.svg" alt="">
        </div>

        <div class="flex gap-5">
            <div class="cursor-pointer bg-[#1A92AA] w-[270px] h-[66px] rounded-full flex items-center justify-center">
                <p class="text-white text-center font-semibold text-[20px]">FY-2024-25</p>
            </div>
            <div class="cursor-pointer w-[270px] h-[66px] relative flex justify-center items-center bg-white rounded-full">
                <div class="absolute left-2 cursor-pointer w-[50px] h-[50px] rounded-full border border-[#DEDEDE] bg-white flex justify-center items-center">
                    <img src="./assets/notification.png" alt="">
                </div>
                <div id="dropdownUserButton" data-dropdown-toggle="dropdownUserMenu"  class="flex items-center gap-2">
                    <img src="./assets/user-icon.svg" class="w-[40px] h-[40px]">
                    <p class="font-semibold text-[16px]">User</p>
                    <button class="cursor-pointer flex items-center focus:outline-none">
                        <img src="./assets/dropdown.svg" alt="" />
                    </button>
                </div>
                <!-- Dropdown Menu -->
                <div id="dropdownUserMenu" class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44">
                <ul class="py-2 text-sm text-gray-700">
                    <li>
                    <a href="#" class="block px-4 py-2 hover:bg-gray-100">Profile</a>
                    </li>
                    <li>
                    <a href="#" class="block px-4 py-2 hover:bg-gray-100">Logout</a>
                    </li>
                </ul>
                </div>
            </div>
        </div>
        
    </div>

</nav>