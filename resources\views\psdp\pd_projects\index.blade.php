@extends('layouts.app')

@section('title', 'PSDP Projects')

@section('content')
<div class="py-10 px-6">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">PSDP Projects</h1>
                <p class="text-gray-600">Manage and monitor all PSDP projects</p>
            </div>
            <div class="flex gap-3">
                <a href="{{ route('psdp.pd-projects.revisions.index') }}"
                   class="bg-gray-500 text-white px-6 py-3 rounded-full font-medium hover:brightness-110">
                    View All Revisions
                </a>
                <a href="{{ route('psdp.pd-projects.create') }}"
                   class="bg-[#1A92AA] text-white px-6 py-3 rounded-full font-medium hover:brightness-110">
                    + Create New Project
                </a>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="bg-white rounded-xl shadow p-6 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="relative">
                    <input type="text" placeholder="Search projects..." 
                           class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-[#1A92AA]">
                    <img src="{{ asset('assets/search-normal.svg') }}" alt="" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4">
                </div>
                
                <select class="px-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-[#1A92AA]">
                    <option>All Departments</option>
                    <option>Ignite</option>
                    <option>NITB</option>
                    <option>NTC</option>
                    <option>SCO</option>
                    <option>USF</option>
                </select>
                
                <select class="px-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-[#1A92AA]">
                    <option>All Status</option>
                    <option>Completed</option>
                    <option>Delayed</option>
                    <option>Not Started</option>
                    <option>Behind</option>
                    <option>On-track</option>
                </select>
                
                <button class="bg-[#1A92AA] text-white px-6 py-2 rounded-full font-medium hover:brightness-110">
                    <img src="{{ asset('assets/export.svg') }}" alt="" class="inline w-4 h-4 mr-2">
                    Export
                </button>
            </div>
        </div>

        <!-- Projects Table -->
        <div class="bg-white rounded-xl shadow overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Project Name
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                PSDP ID
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Department
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Approving Authority
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($projects as $project)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ $project->name }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ $project->psdp_id }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ $project->department->name }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ $project->approvingAuthority->name }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                    Active
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <a href="{{ route('psdp.pd-projects.show', $project->id) }}"
                                       class="text-[#1A92AA] hover:text-blue-900">View</a>
                                    <a href="{{ route('psdp.pd-projects.revisions.create', $project->id) }}"
                                       class="text-green-600 hover:text-green-900">Add Revision</a>
                                    <a href="{{ route('psdp.pd-projects.edit', $project->id) }}"
                                       class="text-indigo-600 hover:text-indigo-900">Edit</a>
                                    <form method="POST" action="{{ route('psdp.pd-projects.destroy', $project->id) }}"
                                          class="inline" onsubmit="return confirm('Are you sure?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="text-red-600 hover:text-red-900">Delete</button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <img src="{{ asset('assets/empty-wallet.png') }}" alt="" class="mx-auto w-16 h-16 mb-4 opacity-50">
                                    <p class="text-lg font-medium">No projects found</p>
                                    <p class="text-sm">Get started by creating your first PSDP project.</p>
                                    <a href="{{ route('psdp.pd-projects.create') }}"
                                       class="inline-block mt-4 bg-[#1A92AA] text-white px-6 py-2 rounded-full font-medium hover:brightness-110">
                                        Create Project
                                    </a>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            
            @if($projects->hasPages())
            <div class="px-6 py-4 border-t border-gray-200">
                {{ $projects->links() }}
            </div>
            @endif
        </div>
    </div>
</div>
@endsection
