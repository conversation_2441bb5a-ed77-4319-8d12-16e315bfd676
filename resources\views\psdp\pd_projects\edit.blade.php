@extends('layouts.app')

@section('title', 'Edit Project - ' . $project->name)

@section('content')
<div class="py-10 px-6">
    <div class="max-w-full mx-auto bg-white rounded-xl shadow p-5 sm:p-12 sm:px-16">
        <!-- Breadcrumb -->
        <div class="flex items-center gap-2 mb-6 text-sm text-gray-600">
            <a href="{{ route('psdp.pd-dashboard') }}" class="hover:text-blue-600">PSDP Projects</a>
            <span>></span>
            <a href="{{ route('psdp.pd-projects.index') }}" class="hover:text-blue-600">Projects</a>
            <span>></span>
            <span class="text-gray-900">Edit {{ $project->name }}</span>
        </div>

        <!-- Header -->
        <h2 class="text-xl sm:text-2xl font-semibold text-center text-gray-900 mb-8">
            Edit PSDP Project
        </h2>

        <!-- Step Indicators -->
        <div class="flex justify-center mb-8">
            <div class="flex items-center space-x-4">
                <div class="flex items-center">
                    <div class="step-indicator active" data-step="1">
                        <span class="step-number">1</span>
                    </div>
                    <span class="ml-2 text-sm font-medium text-gray-900">Project Details</span>
                </div>
                <div class="w-8 h-0.5 bg-gray-300"></div>
                <div class="flex items-center">
                    <div class="step-indicator" data-step="2">
                        <span class="step-number">2</span>
                    </div>
                    <span class="ml-2 text-sm font-medium text-gray-500">Budget Allocations</span>
                </div>
                <div class="w-8 h-0.5 bg-gray-300"></div>
                <div class="flex items-center">
                    <div class="step-indicator" data-step="3">
                        <span class="step-number">3</span>
                    </div>
                    <span class="ml-2 text-sm font-medium text-gray-500">Deliverables & Tasks</span>
                </div>
            </div>
        </div>

        <!-- Multi-step Form -->
        <form id="projectForm" method="POST" action="{{ route('psdp.pd-projects.update', $project->id) }}" novalidate>
            @csrf
            @method('PUT')

            <!-- Step 1: Project Details -->
            <div class="form-step" data-step="1">
                <h3 class="text-lg font-semibold text-gray-800 mb-6">Project Information</h3>

                @if(auth()->user()->hasRole('PD'))
                    <!-- PD User: Only show Project Name (Ministry and Department are hidden) -->
                    @php
                        $userMinistry = auth()->user()->userMinistries()->with(['ministry', 'department'])->first();
                    @endphp

                    <!-- Hidden fields for PD users -->
                    @if($userMinistry)
                        <input type="hidden" name="ministry_id" value="{{ $userMinistry->ministry_id }}">
                        <input type="hidden" name="department_id" value="{{ $userMinistry->department_id }}">
                    @endif

                    <div class="grid grid-cols-1 gap-6 mb-6">
                        <div class="relative mt-4 w-full mx-auto">
                            <input type="text" name="name" id="project_name" required value="{{ $project->name }}"
                                   class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                                   placeholder="Enter project name" />
                            <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                Project Name*
                            </label>
                        </div>
                    </div>
                @else
                    <!-- Super Admin and Ministry Users -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div class="relative mt-4 w-full mx-auto">
                            <input type="text" name="name" id="project_name" required value="{{ $project->name }}"
                                   class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                                   placeholder="Enter project name" />
                            <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                Project Name*
                            </label>
                        </div>

                        @if(auth()->user()->hasRole('super admin'))
                        <!-- Ministry Dropdown for Super Admin -->
                        <div class="relative mt-4 w-full mx-auto">
                            <select name="ministry_id" id="ministry_id" required
                                    class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0 appearance-none">
                                <option value="">Select ministry</option>
                                @foreach($ministries as $ministry)
                                    <option value="{{ $ministry->id }}" {{ $project->ministry_id == $ministry->id ? 'selected' : '' }}>{{ $ministry->name }}</option>
                                @endforeach
                            </select>
                            <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                Ministry*
                            </label>
                            <div class="pointer-events-none absolute inset-y-0 right-4 flex items-center text-gray-500">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                    <path d="M19 9l-7 7-7-7" />
                                </svg>
                            </div>
                        </div>
                        @elseif(auth()->user()->hasRole('admin'))
                            @php
                                $userMinistry = auth()->user()->userMinistries()->with(['ministry', 'department'])->first();
                            @endphp

                            <!-- Hidden ministry_id for admin users -->
                            @if($userMinistry)
                                <input type="hidden" name="ministry_id" value="{{ $userMinistry->ministry_id }}">
                            @endif

                            <!-- Ministry User: Show Ministry Name (Read-only) -->
                            <div class="relative mt-4 w-full mx-auto">
                                <input type="text" value="{{ $userMinistry->ministry->name ?? 'No Ministry Assigned' }}" readonly
                                       class="block w-full text-sm text-gray-500 bg-gray-100 rounded-full border py-3 pl-4 pr-4 outline-0" />
                                <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                    Ministry
                                </label>
                            </div>
                        @endif
                    </div>

                    <!-- Show Department Dropdown for Super Admin and Ministry Users -->
                    <div class="grid grid-cols-1 gap-6 mb-6">
                        <div class="relative mt-4 w-full mx-auto">
                            <select name="department_id" id="department_id" required
                                    class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0 appearance-none">
                                <option value="">Select department</option>
                                @foreach($departments as $department)
                                    <option value="{{ $department->id }}" {{ $project->department_id == $department->id ? 'selected' : '' }}>
                                        {{ $department->name }}
                                    </option>
                                @endforeach
                            </select>
                            <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                Department*
                            </label>
                            <div class="pointer-events-none absolute inset-y-0 right-4 flex items-center text-gray-500">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                    <path d="M19 9l-7 7-7-7" />
                                </svg>
                            </div>
                        </div>
                    </div>
                @endif

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div class="relative mt-2 w-full mx-auto">
                        <input type="text" name="psdp_no" id="psdp_no" required value="{{ $project->psdp_no }}"
                               class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                               placeholder="Enter PSDP No" />
                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                            PSDP No*
                        </label>
                    </div>

                    <div class="relative mt-2 w-full mx-auto">
                        <input type="text" name="psdp_id" id="psdp_id" required value="{{ $project->psdp_id }}"
                               class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                               placeholder="Enter PSDP ID" />
                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                            PSDP ID*
                        </label>
                    </div>
                </div>

                <div class="grid grid-cols-1 gap-6 mb-6">
                    <div class="relative mt-2 w-full mx-auto">
                        <select name="approving_authority_id" id="approving_authority_id" required
                                class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0 appearance-none">
                            <option value="">Select approving authority</option>
                            @foreach($approvingAuthorities as $authority)
                                <option value="{{ $authority->id }}" {{ $project->approving_authority_id == $authority->id ? 'selected' : '' }}>
                                    {{ $authority->name }}
                                </option>
                            @endforeach
                        </select>
                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                            Approving Authority*
                        </label>
                        <div class="pointer-events-none absolute inset-y-0 right-4 flex items-center text-gray-500">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                <path d="M19 9l-7 7-7-7" />
                            </svg>
                        </div>
                    </div>
                </div>


                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div class="relative mt-2 w-full mx-auto">
                        <input type="date" name="authority_approved_date" id="authority_approved_date"
                               value="{{ $project->authority_approved_date ? $project->authority_approved_date->format('Y-m-d') : '' }}"
                               class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0" />
                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                            Authority Approved Date
                        </label>
                    </div>
                    <div class="relative mt-2 w-full mx-auto">
                        <input type="date" name="admin_approval_date" id="admin_approval_date"
                               value="{{ $project->admin_approval_date ? $project->admin_approval_date->format('Y-m-d') : '' }}"
                               class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0" />
                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                            Admin Approval Date
                        </label>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div class="relative mt-2 w-full mx-auto">
                        <input type="date" name="completion_date_pc1" id="completion_date_pc1"
                               value="{{ $project->completion_date_pc1 ? $project->completion_date_pc1->format('Y-m-d') : '' }}"
                               class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0" />
                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                            Completion Date PC1
                        </label>
                    </div>
                    <div class="relative mt-2 w-full mx-auto">
                        <input type="date" name="likely_completion_date" id="likely_completion_date"
                               value="{{ $project->likely_completion_date ? $project->likely_completion_date->format('Y-m-d') : '' }}"
                               class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0" />
                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                            Likely Completion Date
                        </label>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <div class="relative mt-2 w-full mx-auto">
                        <input type="number" name="total_cost" id="total_cost" step="0.001" min="0"
                               value="{{ $project->total_cost }}"
                               class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                               placeholder="0.000" />
                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                            Total Cost (Million PKR)
                        </label>
                        <p class="text-xs text-gray-500 mt-1 ml-4">Enter amount in millions (e.g., 572.8 for 572.8 million PKR)</p>
                    </div>
                    <div class="relative mt-2 w-full mx-auto">
                        <input type="number" name="lc_amount" id="lc_amount" step="0.001" min="0"
                               value="{{ $project->lc_amount }}"
                               class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                               placeholder="0.000" />
                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                            LC Amount (Million PKR)
                        </label>
                        <p class="text-xs text-gray-500 mt-1 ml-4">Enter amount in millions</p>
                    </div>
                    <div class="relative mt-2 w-full mx-auto">
                        <input type="number" name="fc_amount" id="fc_amount" step="0.001" min="0"
                               value="{{ $project->fc_amount }}"
                               class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                               placeholder="0.000" />
                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                            FC Amount (Million USD)
                        </label>
                        <p class="text-xs text-gray-500 mt-1 ml-4">Enter amount in millions</p>
                    </div>
                    <div class="relative mt-2 w-full mx-auto">
                        <input type="number" name="grand_amount" id="grand_amount" step="0.001" min="0" readonly
                               value="{{ $project->grand_amount }}"
                               class="block w-full text-sm text-gray-900 bg-gray-100 rounded-full border py-3 pl-4 pr-4 outline-0"
                               placeholder="0.000" />
                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                            Grand Amount (Million PKR)
                        </label>
                        <p class="text-xs text-gray-500 mt-1 ml-4">Auto-calculated from total cost</p>
                    </div>
                </div>
            </div>

            <!-- Step 2: Budget Allocations -->
            <div class="form-step" data-step="2" style="display: none;">
                <h3 class="text-lg font-semibold text-gray-800 mb-6">Budget Allocations</h3>

                <div id="allocations-container">
                    @if($project->allocations->count() > 0)
                        @foreach($project->allocations as $index => $allocation)
                        <div class="allocation-item border rounded-lg p-4 mb-4">
                            <div class="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
                                <h3 class="text-md font-semibold text-gray-800 whitespace-nowrap mr-4">
                                    Financial Year Allocation {{ $index + 1 }} :
                                </h3>
                                <div class="flex-grow hidden sm:flex h-px bg-[repeating-linear-gradient(to_right,_#ccc,_#ccc_10px,_transparent_10px,_transparent_20px)]"></div>
                                @if($index > 0)
                                <button type="button" class="remove-allocation text-red-600 px-4 py-2 text-sm rounded-full font-medium border border-red-600 hover:bg-red-50">
                                    Remove
                                </button>
                                @endif
                            </div>
                        <div class="grid grid-cols-1 gap-6 mb-4">
                            <div class="relative mt-4 w-full mx-auto">
                                <select name="allocations[{{ $index }}][financial_year_id]" required class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0 appearance-none">
                                    <option value="">Select financial year</option>
                                    @foreach($financialYears as $year)
                                        <option value="{{ $year->id }}" {{ $allocation->financial_year_id == $year->id ? 'selected' : '' }}>{{ $year->name }}</option>
                                    @endforeach
                                </select>
                                <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                    Financial Year*
                                </label>
                                <div class="pointer-events-none absolute inset-y-0 right-4 flex items-center text-gray-500">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                        <path d="M19 9l-7 7-7-7" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-4">
                            <div class="relative mt-4 w-full mx-auto">
                                <input type="number" name="allocations[{{ $index }}][allocation_amount]" step="0.001" required
                                       value="{{ $allocation->allocation_amount }}"
                                       class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                                       placeholder="0.000" />
                                <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                    Allocation Amount (Million PKR)*
                                </label>
                            </div>
                            <div class="relative mt-4 w-full mx-auto">
                                <input type="number" name="allocations[{{ $index }}][expense_amount]" step="0.001"
                                       value="{{ $allocation->expense_amount }}"
                                       class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                                       placeholder="0.000" />
                                <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                    Expense Amount (Million PKR)
                                </label>
                            </div>
                            <div class="relative mt-4 w-full mx-auto">
                                <input type="number" name="allocations[{{ $index }}][release_amount]" step="0.001"
                                       value="{{ $allocation->release_amount }}"
                                       class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                                       placeholder="0.000" />
                                <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                    Release Amount (Million PKR)
                                </label>
                            </div>
                        </div>

                        <!-- Quarterly Breakdown -->
                        <div class="mt-6">
                            <h5 class="text-md font-semibold text-gray-800 mb-4">Quarterly Breakdown</h5>
                            <div class="overflow-x-auto">
                                <table class="w-full border-collapse border border-gray-300 rounded-lg">
                                    <thead>
                                        <tr class="bg-gray-50">
                                            <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Quarter</th>
                                            <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Release Amount (Million PKR)</th>
                                            <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Expense Amount (Million PKR)</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @for($q = 0; $q < 4; $q++)
                                        @php
                                            $quarter = $allocation->quarters->where('quarter', 'Q' . ($q + 1))->first();
                                        @endphp
                                        <tr class="hover:bg-gray-50">
                                            <td class="border border-gray-300 px-4 py-3 text-sm font-medium text-gray-700">Q{{ $q + 1 }}</td>
                                            <td class="border border-gray-300 px-4 py-3">
                                                <input type="number" name="allocations[{{ $index }}][quarters][{{ $q }}][release_amount]" step="0.001"
                                                       value="{{ $quarter ? $quarter->release_amount : '' }}"
                                                       class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                                       placeholder="0.000" />
                                            </td>
                                            <td class="border border-gray-300 px-4 py-3">
                                                <input type="number" name="allocations[{{ $index }}][quarters][{{ $q }}][expense_amount]" step="0.001"
                                                       value="{{ $quarter ? $quarter->expense_amount : '' }}"
                                                       class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                                       placeholder="0.000" />
                                            </td>
                                        </tr>
                                        @endfor
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        </div>
                        @endforeach
                    @else
                        <!-- Default allocation if none exist -->
                        <div class="allocation-item border rounded-lg p-4 mb-4">
                            <div class="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
                                <h3 class="text-md font-semibold text-gray-800 whitespace-nowrap mr-4">
                                    Financial Year Allocation 1 :
                                </h3>
                                <div class="flex-grow hidden sm:flex h-px bg-[repeating-linear-gradient(to_right,_#ccc,_#ccc_10px,_transparent_10px,_transparent_20px)]"></div>
                            </div>

                            <div class="grid grid-cols-1 gap-6 mb-4">
                                <div class="relative mt-4 w-full mx-auto">
                                    <select name="allocations[0][financial_year_id]" required class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0 appearance-none">
                                        <option value="">Select financial year</option>
                                        @foreach($financialYears as $year)
                                            <option value="{{ $year->id }}">{{ $year->name }}</option>
                                        @endforeach
                                    </select>
                                    <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                        Financial Year*
                                    </label>
                                    <div class="pointer-events-none absolute inset-y-0 right-4 flex items-center text-gray-500">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                            <path d="M19 9l-7 7-7-7" />
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-4">
                                <div class="relative mt-4 w-full mx-auto">
                                    <input type="number" name="allocations[0][allocation_amount]" step="0.01" required
                                           class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                                           placeholder="0.00" />
                                    <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                        Allocation Amount*
                                    </label>
                                </div>
                                <div class="relative mt-4 w-full mx-auto">
                                    <input type="number" name="allocations[0][expense_amount]" step="0.01"
                                           class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                                           placeholder="0.00" />
                                    <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                        Expense Amount
                                    </label>
                                </div>
                                <div class="relative mt-4 w-full mx-auto">
                                    <input type="number" name="allocations[0][release_amount]" step="0.01"
                                           class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                                           placeholder="0.00" />
                                    <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                        Release Amount
                                    </label>
                                </div>
                            </div>

                            <!-- Quarterly Breakdown -->
                            <div class="mt-6">
                                <h5 class="text-md font-semibold text-gray-800 mb-4">Quarterly Breakdown</h5>
                                <div class="overflow-x-auto">
                                    <table class="w-full border-collapse border border-gray-300 rounded-lg">
                                        <thead>
                                            <tr class="bg-gray-50">
                                                <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Quarter</th>
                                                <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Release Amount (Million PKR)</th>
                                                <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Expense Amount (Million PKR)</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @for($q = 0; $q < 4; $q++)
                                            <tr class="hover:bg-gray-50">
                                                <td class="border border-gray-300 px-4 py-3 text-sm font-medium text-gray-700">Q{{ $q + 1 }}</td>
                                                <td class="border border-gray-300 px-4 py-3">
                                                    <input type="number" name="allocations[0][quarters][{{ $q }}][release_amount]" step="0.01"
                                                           class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                                           placeholder="0.00" />
                                                </td>
                                                <td class="border border-gray-300 px-4 py-3">
                                                    <input type="number" name="allocations[0][quarters][{{ $q }}][expense_amount]" step="0.01"
                                                           class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                                           placeholder="0.00" />
                                                </td>
                                            </tr>
                                            @endfor
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>

                <button type="button" id="addAllocation" class="text-white px-4 py-2 text-sm rounded-full font-medium" style="background-color: #1A92AA;">
                    + Add Another Allocation
                </button>
            </div>

            <!-- Step 3: Deliverables & Tasks -->
            <div class="form-step" data-step="3" style="display: none;">
                <h3 class="text-lg font-semibold text-gray-800 mb-6">Deliverables & Tasks</h3>

                <div id="deliverables-container">
                    @if($project->deliverables->count() > 0)
                        @foreach($project->deliverables as $dIndex => $deliverable)
                        <div class="deliverable-item border rounded-lg p-4 mb-6">
                            <div class="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
                                <h3 class="text-md font-semibold text-gray-800 whitespace-nowrap mr-4">
                                    Output/Deliverable-{{ $dIndex + 1 }} :
                                </h3>
                                <div class="flex-grow hidden sm:flex h-px bg-[repeating-linear-gradient(to_right,_#ccc,_#ccc_10px,_transparent_10px,_transparent_20px)]"></div>
                                @if($dIndex > 0)
                                <button type="button" class="remove-deliverable text-red-600 px-4 py-2 text-sm rounded-full font-medium border border-red-600 hover:bg-red-50">
                                    Remove
                                </button>
                                @endif
                            </div>
                        <div class="grid grid-cols-1 gap-6 mb-4">
                            <div class="relative mt-4 w-full mx-auto">
                                <input type="text" name="deliverables[{{ $dIndex }}][name]" required
                                       value="{{ $deliverable->name }}"
                                       class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                                       placeholder="Enter deliverable name" />
                                <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                    Deliverable Name*
                                </label>
                            </div>
                        </div>

                        <div class="tasks-container">
                            @foreach($deliverable->tasks as $tIndex => $task)
                            <div class="task-item border-l-4 border-blue-200 pl-4 mb-4">
                                <h5 class="font-medium mb-3">Task {{ $tIndex + 1 }}</h5>

                                <!-- Task Code and Description -->
                                <div class="flex flex-col sm:flex-row gap-6 mb-6">
                                    <div class="relative mt-4 w-full sm:w-[30%]">
                                        <input type="text" name="deliverables[{{ $dIndex }}][tasks][{{ $tIndex }}][code]" required
                                               value="{{ $task->code }}"
                                               class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                                               placeholder="1.1" />
                                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                            Code*
                                        </label>
                                    </div>
                                    <div class="relative mt-4 w-full sm:w-[70%]">
                                        <input type="text" name="deliverables[{{ $dIndex }}][tasks][{{ $tIndex }}][description]" required
                                               value="{{ $task->description }}"
                                               class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                                               placeholder="Enter task description" />
                                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                            Task Description*
                                        </label>
                                    </div>
                                </div>

                                <!-- Date Inputs -->
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                    <div class="relative mt-2 w-full mx-auto">
                                        <input type="date" name="deliverables[{{ $dIndex }}][tasks][{{ $tIndex }}][start_date]" required
                                               value="{{ $task->start_date ? $task->start_date->format('Y-m-d') : '' }}"
                                               class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0" />
                                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                            Start Date*
                                        </label>
                                    </div>
                                    <div class="relative mt-2 w-full mx-auto">
                                        <input type="date" name="deliverables[{{ $dIndex }}][tasks][{{ $tIndex }}][end_date]" required
                                               value="{{ $task->end_date ? $task->end_date->format('Y-m-d') : '' }}"
                                               class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0" />
                                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                            End Date*
                                        </label>
                                    </div>
                                </div>

                                <!-- Progress Inputs -->
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                    <div class="relative -mt-2 w-full mx-auto">
                                        <input type="number" name="deliverables[{{ $dIndex }}][tasks][{{ $tIndex }}][planned_complete]" min="0" max="100" step="0.01" required
                                               value="{{ $task->planned_complete }}"
                                               class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                                               placeholder="0" />
                                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                            Planned % Complete*
                                        </label>
                                    </div>
                                    <div class="relative -mt-2 w-full mx-auto">
                                        <input type="number" name="deliverables[{{ $dIndex }}][tasks][{{ $tIndex }}][actual_complete]" min="0" max="100" step="0.01" required
                                               value="{{ $task->actual_complete }}"
                                               class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                                               placeholder="0" />
                                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                            Actual % Complete*
                                        </label>
                                    </div>
                                </div>

                                <!-- Status and Remarks -->
                                <div class="flex flex-col sm:flex-row gap-6 mb-6">
                                    <div class="relative -mt-2 w-full sm:w-[30%]">
                                        <select name="deliverables[{{ $dIndex }}][tasks][{{ $tIndex }}][status_id]" required
                                                class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0 appearance-none">
                                            <option value="">Select status</option>
                                            @foreach($statuses as $status)
                                                <option value="{{ $status->id }}" {{ $task->status_id == $status->id ? 'selected' : '' }}>{{ $status->name }}</option>
                                            @endforeach
                                        </select>
                                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                            Status*
                                        </label>
                                        <div class="pointer-events-none absolute inset-y-0 right-4 flex items-center text-gray-500">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                                <path d="M19 9l-7 7-7-7" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="relative -mt-2 w-full sm:w-[70%]">
                                        <textarea name="deliverables[{{ $dIndex }}][tasks][{{ $tIndex }}][remarks]"
                                                  class="block w-full text-sm text-gray-900 bg-transparent rounded-lg border py-3 pl-4 pr-4 outline-0 resize-none"
                                                  rows="3" placeholder="Enter remarks (optional)">{{ $task->remarks ?? '' }}</textarea>
                                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                            Remarks
                                        </label>
                                    </div>
                                </div>

                                @if($tIndex > 0)
                                <button type="button" class="remove-task text-red-600 px-4 py-2 text-sm rounded-full font-medium border border-red-600 hover:bg-red-50">
                                    Remove Task
                                </button>
                                @endif
                            </div>
                            @endforeach
                        </div>

                        <button type="button" class="add-task text-white px-4 py-2 text-sm rounded-full font-medium mb-4" style="background-color: #1A92AA;">
                            + Add Task
                        </button>
                        </div>
                        @endforeach
                    @else
                        <!-- Default deliverable if none exist -->
                        <div class="deliverable-item border rounded-lg p-4 mb-6">
                            <div class="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
                                <h3 class="text-md font-semibold text-gray-800 whitespace-nowrap mr-4">
                                    Output/Deliverable-1 :
                                </h3>
                                <div class="flex-grow hidden sm:flex h-px bg-[repeating-linear-gradient(to_right,_#ccc,_#ccc_10px,_transparent_10px,_transparent_20px)]"></div>
                            </div>

                            <div class="relative mt-4 w-full mx-auto mb-4">
                                <input type="text" name="deliverables[0][name]" required
                                       class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                                       placeholder="Enter deliverable name" />
                                <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                    Deliverable Name*
                                </label>
                            </div>

                            <div class="tasks-container">
                                <div class="task-item border-l-4 border-blue-200 pl-4 mb-4">
                                    <h5 class="font-medium mb-3">Task 1</h5>

                                    <!-- Task Code and Description -->
                                    <div class="flex flex-col sm:flex-row gap-6 mb-6">
                                        <div class="relative mt-4 w-full sm:w-[30%]">
                                            <input type="text" name="deliverables[0][tasks][0][code]" required
                                                   class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                                                   placeholder="1.1" />
                                            <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                                Code*
                                            </label>
                                        </div>
                                        <div class="relative mt-4 w-full sm:w-[70%]">
                                            <input type="text" name="deliverables[0][tasks][0][description]" required
                                                   class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                                                   placeholder="Enter task description" />
                                            <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                                Task Description*
                                            </label>
                                        </div>
                                    </div>

                                    <!-- Date Inputs -->
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                        <div class="relative mt-2 w-full mx-auto">
                                            <input type="date" name="deliverables[0][tasks][0][start_date]" required
                                                   class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0" />
                                            <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                                Start Date*
                                            </label>
                                        </div>
                                        <div class="relative mt-2 w-full mx-auto">
                                            <input type="date" name="deliverables[0][tasks][0][end_date]" required
                                                   class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0" />
                                            <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                                End Date*
                                            </label>
                                        </div>
                                    </div>

                                    <!-- Progress Inputs -->
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                        <div class="relative -mt-2 w-full mx-auto">
                                            <input type="number" name="deliverables[0][tasks][0][planned_complete]" min="0" max="100" step="0.01" required
                                                   class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                                                   placeholder="0" />
                                            <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                                Planned % Complete*
                                            </label>
                                        </div>
                                        <div class="relative -mt-2 w-full mx-auto">
                                            <input type="number" name="deliverables[0][tasks][0][actual_complete]" min="0" max="100" step="0.01" required
                                                   class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                                                   placeholder="0" />
                                            <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                                Actual % Complete*
                                            </label>
                                        </div>
                                    </div>

                                    <!-- Status and Remarks -->
                                    <div class="flex flex-col sm:flex-row gap-6 mb-6">
                                        <div class="relative -mt-2 w-full sm:w-[30%]">
                                            <select name="deliverables[0][tasks][0][status_id]" required
                                                    class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0 appearance-none">
                                                <option value="">Select status</option>
                                                @foreach($statuses as $status)
                                                    <option value="{{ $status->id }}">{{ $status->name }}</option>
                                                @endforeach
                                            </select>
                                            <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                                Status*
                                            </label>
                                            <div class="pointer-events-none absolute inset-y-0 right-4 flex items-center text-gray-500">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                                    <path d="M19 9l-7 7-7-7" />
                                                </svg>
                                            </div>
                                        </div>
                                        <div class="relative -mt-2 w-full sm:w-[70%]">
                                            <textarea name="deliverables[0][tasks][0][remarks]"
                                                      class="block w-full text-sm text-gray-900 bg-transparent rounded-lg border py-3 pl-4 pr-4 outline-0 resize-none"
                                                      rows="3" placeholder="Enter remarks (optional)"></textarea>
                                            <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                                Remarks
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <button type="button" class="add-task text-white px-4 py-2 text-sm rounded-full font-medium mb-4" style="background-color: #1A92AA;">
                                + Add Task
                            </button>
                        </div>
                    @endif
                </div>

                <button type="button" id="addDeliverable" class="text-white px-4 py-2 text-sm rounded-full font-medium" style="background-color: #1A92AA;">
                    + Add Another Deliverable
                </button>
            </div>

            <!-- Navigation Buttons -->
            <div class="flex justify-center mt-8">
                <div class="flex flex-col sm:flex-row justify-center gap-4 w-full">
                    <button type="button" id="prevBtn"
                            class="w-full sm:w-36 text-black text-sm font-medium rounded-full px-4 py-2.5 hover:brightness-105"
                            style="background-color: #D6DCE4; display: none;">
                        Previous
                    </button>
                    <a href="{{ route('psdp.pd-projects.show', $project->id) }}"
                       class="w-full sm:w-36 text-black text-sm font-medium rounded-full px-4 py-2.5 hover:brightness-105 text-center"
                       style="background-color: #D6DCE4;">
                        Cancel
                    </a>
                    <button type="button" id="nextBtn"
                            class="w-full sm:w-36 text-white text-sm font-medium rounded-full px-4 py-2.5 hover:brightness-110"
                            style="background-color: #1A92AA;">
                        Next
                    </button>
                    <button type="submit" id="submitBtn"
                            class="w-full sm:w-36 text-white text-sm font-medium rounded-full px-4 py-2.5 hover:brightness-110"
                            style="background-color: #1A92AA; display: none;">
                        Update Project
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<style>
.step-indicator {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #E5E7EB;
    color: #6B7280;
    font-weight: 500;
    font-size: 14px;
}

.step-indicator.active {
    background-color: #1A92AA;
    color: white;
}

.step-indicator.completed {
    background-color: #10B981;
    color: white;
}
</style>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
let currentStep = 1;
const totalSteps = 3;

// Initialize form
$(document).ready(function() {
    updateStepIndicators();
    updateNavigationButtons();

    // Add real-time date validation
    $('#admin_approval_date, #completion_date_pc1, #likely_completion_date').on('change', function() {
        validateDates();
    });

    // Auto-fill grand_amount when total_cost changes
    $('#total_cost').on('input', function() {
        const totalCost = parseFloat($(this).val()) || 0;
        $('#grand_amount').val(totalCost.toFixed(3));
    });
});

// Next button click
$('#nextBtn').click(function() {
    if (validateCurrentStep()) {
        if (currentStep < totalSteps) {
            currentStep++;
            showStep(currentStep);
            updateStepIndicators();
            updateNavigationButtons();
        }
    }
});

// Previous button click
$('#prevBtn').click(function() {
    if (currentStep > 1) {
        currentStep--;
        showStep(currentStep);
        updateStepIndicators();
        updateNavigationButtons();
    }
});

// Date validation function
function validateDates() {
    let isValid = true;
    const adminApprovalDate = $('#admin_approval_date').val();
    const completionDatePC1 = $('#completion_date_pc1').val();
    const likelyCompletionDate = $('#likely_completion_date').val();

    // Clear previous date error messages
    $('.date-error').remove();
    $('#admin_approval_date, #completion_date_pc1, #likely_completion_date').removeClass('border-red-500');

    // Only validate if both dates are provided
    if (adminApprovalDate && completionDatePC1) {
        const adminDate = new Date(adminApprovalDate);
        const completionDate = new Date(completionDatePC1);

        // Check if dates are valid
        if (isNaN(adminDate.getTime()) || isNaN(completionDate.getTime())) {
            return isValid; // Skip validation if dates are invalid
        }

        if (completionDate <= adminDate) {
            isValid = false;
            $('#completion_date_pc1').addClass('border-red-500');
            $('#completion_date_pc1').parent().append('<div class="date-error text-red-500 text-xs mt-1">Completion Date PC1 must be greater than Admin Approval Date</div>');
        }
    }

    // Only validate if both dates are provided
    if (completionDatePC1 && likelyCompletionDate) {
        const completionDate = new Date(completionDatePC1);
        const likelyDate = new Date(likelyCompletionDate);

        // Check if dates are valid
        if (isNaN(completionDate.getTime()) || isNaN(likelyDate.getTime())) {
            return isValid; // Skip validation if dates are invalid
        }

        if (likelyDate < completionDate) {
            isValid = false;
            $('#likely_completion_date').addClass('border-red-500');
            $('#likely_completion_date').parent().append('<div class="date-error text-red-500 text-xs mt-1">Likely Completion Date must be greater than or equal to Completion Date PC1</div>');
        }
    }

    return isValid;
}

function validateCurrentStep() {
    let isValid = true;
    const currentStepElement = $(`.form-step[data-step="${currentStep}"]`);

    // Clear previous error messages
    currentStepElement.find('.error-message, .date-error').remove();
    currentStepElement.find('.border-red-500').removeClass('border-red-500');

    // Validate required fields
    currentStepElement.find('input[required], select[required], textarea[required]').each(function() {
        if (!$(this).val()) {
            isValid = false;
            $(this).addClass('border-red-500');
            $(this).after('<span class="error-message text-red-500 text-xs mt-1">This field is required</span>');
        }
    });

    // Additional date validation for step 1
    if (currentStep === 1) {
        isValid = validateDates() && isValid;
    }

    return isValid;
}

function showStep(step) {
    $('.form-step').hide();
    $(`.form-step[data-step="${step}"]`).show();
}

function updateStepIndicators() {
    $('.step-indicator').removeClass('active completed');
    $('.step-indicator').each(function() {
        const stepNum = parseInt($(this).data('step'));
        if (stepNum < currentStep) {
            $(this).addClass('completed');
        } else if (stepNum === currentStep) {
            $(this).addClass('active');
        }
    });
}

function updateNavigationButtons() {
    $('#prevBtn').toggle(currentStep > 1);
    $('#nextBtn').toggle(currentStep < totalSteps);
    $('#submitBtn').toggle(currentStep === totalSteps);
}

// Function to validate all steps
function validateAllSteps() {
    let isValid = true;

    // Validate all steps
    for (let step = 1; step <= totalSteps; step++) {
        const stepElement = $(`.form-step[data-step="${step}"]`);

        // Clear previous error messages
        stepElement.find('.error-message, .date-error').remove();
        stepElement.find('.border-red-500').removeClass('border-red-500');

        // Validate required fields
        stepElement.find('input[required], select[required], textarea[required]').each(function() {
            if (!$(this).val()) {
                isValid = false;
                $(this).addClass('border-red-500');
                $(this).after('<span class="error-message text-red-500 text-xs mt-1">This field is required</span>');
            }
        });

        // Validate dates for step 1
        if (step === 1) {
            if (!validateDates()) {
                isValid = false;
            }
        }
    }

    return isValid;
}

// Form submission
$('#projectForm').submit(function(e) {
    e.preventDefault();

    console.log('Form submission started');

    if (!validateAllSteps()) {
        console.log('Validation failed');
        // If validation fails, go to the first step with errors
        for (let step = 1; step <= totalSteps; step++) {
            const stepElement = $(`.form-step[data-step="${step}"]`);
            if (stepElement.find('.error-message, .date-error, .border-red-500').length > 0) {
                currentStep = step;
                showStep(currentStep);
                updateStepIndicators();
                updateNavigationButtons();
                break;
            }
        }
        return;
    }

    console.log('Validation passed, submitting form');

    // Show loading state
    const submitBtn = $('#submitBtn');
    const originalText = submitBtn.text();
    submitBtn.prop('disabled', true).text('Updating...');

    // Clear previous errors
    $('.error-message').remove();
    $('.border-red-500').removeClass('border-red-500');

    // Collect form data
    const formData = new FormData(this);

    console.log('Form data collected');

    // Submit via AJAX
    $.ajax({
        url: $(this).attr('action'),
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            console.log('Success response:', response);
            submitBtn.prop('disabled', false).text(originalText);

            if (response.success) {
                alert('Project updated successfully!');
                window.location.href = response.redirect || "{{ route('psdp.pd-projects.index') }}";
            }
        },
        error: function(xhr) {
            console.log('Error response:', xhr);
            console.log('Response JSON:', xhr.responseJSON);
            console.log('Response Text:', xhr.responseText);
            submitBtn.prop('disabled', false).text(originalText);

            if (xhr.status === 422) {
                // Validation errors
                const errors = xhr.responseJSON?.errors;
                if (errors) {
                    // Clear previous errors
                    $('.error-message').remove();
                    $('.border-red-500').removeClass('border-red-500');

                    Object.keys(errors).forEach(function(key) {
                        const input = $(`[name="${key}"]`);
                        input.addClass('border-red-500');
                        if (!input.siblings('.error-message').length) {
                            input.after(`<span class="error-message text-red-500 text-xs mt-1">${errors[key][0]}</span>`);
                        }
                    });

                    alert('Please fix the validation errors and try again.');
                }
            } else {
                // Server error
                const message = xhr.responseJSON?.message || 'An error occurred while updating the project.';
                alert('Error: ' + message);
            }
        }
    });
});

// Add allocation functionality
let allocationIndex = {{ count($project->allocations) }};
$('#addAllocation').click(function() {
    const allocationHtml = `
        <div class="allocation-item border rounded-lg p-4 mb-4">
            <h4 class="font-semibold mb-4">Financial Year Allocation ${allocationIndex + 1}</h4>
            <div class="grid grid-cols-1 gap-6 mb-4">
                <div class="relative mt-4 w-full mx-auto">
                    <select name="allocations[${allocationIndex}][financial_year_id]" required class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0 appearance-none">
                        <option value="">Select financial year</option>
                        @foreach($financialYears as $year)
                            <option value="{{ $year->id }}">{{ $year->name }}</option>
                        @endforeach
                    </select>
                    <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                        Financial Year*
                    </label>
                    <div class="pointer-events-none absolute inset-y-0 right-4 flex items-center text-gray-500">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                            <path d="M19 9l-7 7-7-7" />
                        </svg>
                    </div>
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-4">
                <div class="relative mt-4 w-full mx-auto">
                    <input type="number" name="allocations[${allocationIndex}][allocation_amount]" step="0.001" required
                           class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                           placeholder="0.000" />
                    <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                        Allocation Amount (Million PKR)*
                    </label>
                </div>
                <div class="relative mt-4 w-full mx-auto">
                    <input type="number" name="allocations[${allocationIndex}][expense_amount]" step="0.001"
                           class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                           placeholder="0.000" />
                    <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                        Expense Amount (Million PKR)
                    </label>
                </div>
                <div class="relative mt-4 w-full mx-auto">
                    <input type="number" name="allocations[${allocationIndex}][release_amount]" step="0.001"
                           class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                           placeholder="0.000" />
                    <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                        Release Amount (Million PKR)
                    </label>
                </div>
            </div>

            <!-- Quarterly Breakdown -->
            <div class="mt-6">
                <h5 class="text-md font-semibold text-gray-800 mb-4">Quarterly Breakdown</h5>
                <div class="overflow-x-auto">
                    <table class="w-full border-collapse border border-gray-300 rounded-lg">
                        <thead>
                            <tr class="bg-gray-50">
                                <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Quarter</th>
                                <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Release Amount (Million PKR)</th>
                                <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Expense Amount (Million PKR)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="hover:bg-gray-50">
                                <td class="border border-gray-300 px-4 py-3 text-sm font-medium text-gray-700">Q1</td>
                                <td class="border border-gray-300 px-4 py-3">
                                    <input type="number" name="allocations[${allocationIndex}][quarters][0][release_amount]" step="0.001"
                                           class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                           placeholder="0.000" />
                                </td>
                                <td class="border border-gray-300 px-4 py-3">
                                    <input type="number" name="allocations[${allocationIndex}][quarters][0][expense_amount]" step="0.001"
                                           class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                           placeholder="0.000" />
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="border border-gray-300 px-4 py-3 text-sm font-medium text-gray-700">Q2</td>
                                <td class="border border-gray-300 px-4 py-3">
                                    <input type="number" name="allocations[${allocationIndex}][quarters][1][release_amount]" step="0.001"
                                           class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                           placeholder="0.000" />
                                </td>
                                <td class="border border-gray-300 px-4 py-3">
                                    <input type="number" name="allocations[${allocationIndex}][quarters][1][expense_amount]" step="0.001"
                                           class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                           placeholder="0.000" />
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="border border-gray-300 px-4 py-3 text-sm font-medium text-gray-700">Q3</td>
                                <td class="border border-gray-300 px-4 py-3">
                                    <input type="number" name="allocations[${allocationIndex}][quarters][2][release_amount]" step="0.001"
                                           class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                           placeholder="0.000" />
                                </td>
                                <td class="border border-gray-300 px-4 py-3">
                                    <input type="number" name="allocations[${allocationIndex}][quarters][2][expense_amount]" step="0.001"
                                           class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                           placeholder="0.000" />
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="border border-gray-300 px-4 py-3 text-sm font-medium text-gray-700">Q4</td>
                                <td class="border border-gray-300 px-4 py-3">
                                    <input type="number" name="allocations[${allocationIndex}][quarters][3][release_amount]" step="0.001"
                                           class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                           placeholder="0.000" />
                                </td>
                                <td class="border border-gray-300 px-4 py-3">
                                    <input type="number" name="allocations[${allocationIndex}][quarters][3][expense_amount]" step="0.001"
                                           class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                           placeholder="0.000" />
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <button type="button" class="remove-allocation text-red-600 px-3 py-1 text-xs rounded-full font-medium border border-red-600 hover:bg-red-50 mt-4">
                Remove Allocation
            </button>
        </div>
    `;
    $('#allocations-container').append(allocationHtml);
    allocationIndex++;
});

// Remove allocation functionality
$(document).on('click', '.remove-allocation', function() {
    $(this).closest('.allocation-item').remove();
});

// Add deliverable functionality
let deliverableIndex = {{ count($project->deliverables) }};
$('#addDeliverable').click(function() {
    const deliverableHtml = `
        <div class="deliverable-item border rounded-lg p-4 mb-4">
            <h4 class="font-semibold mb-4">Deliverable ${deliverableIndex + 1}</h4>
            <div class="grid grid-cols-1 gap-6 mb-4">
                <div class="relative mt-4 w-full mx-auto">
                    <input type="text" name="deliverables[${deliverableIndex}][name]" required
                           class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                           placeholder="Enter deliverable name" />
                    <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                        Deliverable Name*
                    </label>
                </div>
            </div>
            <div class="tasks-container">
                <div class="task-item bg-gray-50 rounded-lg p-4 mb-4">
                    <h5 class="font-medium mb-3">Task 1</h5>
                    <!-- Task fields would go here -->
                </div>
            </div>
            <button type="button" class="add-task text-green-600 px-3 py-1 text-xs rounded-full font-medium border border-green-600 hover:bg-green-50 mb-4">
                + Add Task
            </button>
            <button type="button" class="remove-deliverable text-red-600 px-3 py-1 text-xs rounded-full font-medium border border-red-600 hover:bg-red-50">
                Remove Deliverable
            </button>
        </div>
    `;
    $('#deliverables-container').append(deliverableHtml);
    deliverableIndex++;
});

// Remove deliverable functionality
$(document).on('click', '.remove-deliverable', function() {
    $(this).closest('.deliverable-item').remove();
});

// Remove task functionality
$(document).on('click', '.remove-task', function() {
    $(this).closest('.task-item').remove();
});

// Cascading Dropdowns for Ministry -> Department (only for Super Admin)
@if(auth()->user()->hasRole('super admin'))
$('#ministry_id').change(function() {
    const ministryId = $(this).val();
    const departmentSelect = $('#department_id');

    // Clear department dropdown
    departmentSelect.html('<option value="">Select department</option>');

    if (ministryId) {
        // Load departments for selected ministry via AJAX
        $.get(`/psdp/api/pd/departments/${ministryId}`)
            .done(function(departments) {
                departments.forEach(department => {
                    departmentSelect.append(`<option value="${department.id}">${department.name}</option>`);
                });
            })
            .fail(function() {
                console.error('Failed to load departments');
            });
    }
});
@endif

// Auto-populate departments for admin users on page load
@if(auth()->user()->hasRole('admin'))
$(document).ready(function() {
    @php
        $userMinistry = auth()->user()->userMinistries()->with(['ministry', 'department'])->first();
    @endphp

    console.log('Admin user ministry data:', @json($userMinistry));

    @if($userMinistry)
    const ministryId = {{ $userMinistry->ministry_id }};
    const departmentSelect = $('#department_id');

    console.log('Loading departments for ministry ID:', ministryId);

    if (ministryId) {
        // Load departments for admin user's ministry
        $.get(`/psdp/api/pd/departments/${ministryId}`)
            .done(function(departments) {
                console.log('Departments loaded:', departments);
                departmentSelect.html('<option value="">Select department</option>');
                departments.forEach(department => {
                    const isSelected = department.id == {{ $project->department_id }} ? 'selected' : '';
                    departmentSelect.append(`<option value="${department.id}" ${isSelected}>${department.name}</option>`);
                });
            })
            .fail(function(xhr, status, error) {
                console.error('Failed to load departments for admin user:', error);
                console.error('Response:', xhr.responseText);
            });
    }
    @else
    console.log('No ministry assignment found for admin user');
    @endif
});
@endif
</script>
@endsection
