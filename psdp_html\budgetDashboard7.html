<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.jsdelivr.net/npm/flowbite@3.1.2/dist/flowbite.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script src="https://cdn.jsdelivr.net/npm/flowbite@3.1.2/dist/flowbite.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <title>MOITT</title>
</head>
<body class="bg-[#ebf3f8]">
    <?php include "./common/header.html" ?>

    <div class="p-5">
        
        <div class="p-5 flex gap-10 bg-white rounded-[14px]">

            <div class="w-[40%] flex flex-col gap-3 justify-center">
                <p class="text-[#0D163A] text-[22px] font-semibold">NITB</p>
                <p class="text-[16px]">National Information Technology Board</p>
            </div>


            <div class="w-[60%]">

                <div class="w-full">
                    <table class="w-full border-separate border-spacing-y-[15px]">
                        <thead class="text-left">
                            <th>Total Projects</th>
                            <th>Total Budget</th>
                            <th>Allocation</th>
                            <th>Expenditure</th>
                            <th>Cumulative Exp</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>0</td>
                                <td>Rs. 572.80 M</td>
                                <td>Rs. 300.00 M</td>
                                <td>Rs. 177.86 M</td>
                                <td>Rs. 281.41 M</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
            </div>
            
        </div>

        <div class="grid gap-5 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 mt-8">
        
            <div class="bg-[#AEE8FF] w-[326px] h-[162px] rounded-[13px] p-5 flex flex-col justify-between w-full">

                <div class="flex items-center gap-3 border-b border-[#40A5BA] pb-3">

                    <div class="flex justify-center items-center rounded-full w-[47px] h-[47px] border border-[#8FC3D2]">
                        <img src="./assets/empty-wallet.png" alt="">
                    </div>

                    <div class="flex flex-col gap-1">
                        <h3 class="text-[17px] text-[#1A92AA] font-semibold">Total Budget Allocation</h3>
                        <div class="flex gap-2 items-center">
                            <img src="./assets/export.svg" alt="">
                            <p class="text-[15px] text-[#1A92AA]">100% of Total Budget</p>
                        </div>
                    </div>
                    
                </div>

                <div class="flex justify-between items-center">
                    <p class="text-[27.5px] text-[#1A92AA] font-semibold">PKR 23929.0M</p>
                    <img src="./assets/arrow-right.svg" class="cursor-pointer">
                </div>
                
            </div>

            <div class="bg-[#FFE3C8] w-[326px] h-[162px] rounded-[13px] p-5 flex flex-col justify-between w-full">

                <div class="flex items-center gap-3 border-b border-[#C67639] pb-3">

                    <div class="flex justify-center items-center rounded-full w-[47px] h-[47px] border border-[#C67639]">
                        <img src="./assets/empty-wallet-brown.svg" alt="">
                    </div>

                    <div class="flex flex-col gap-1">
                        <h3 class="text-[17px] text-[#C67639] font-semibold">Final Allocation</h3>
                        <div class="flex gap-2 items-center">
                            <img src="./assets/export-brown.svg" alt="">
                            <p class="text-[15px] text-[#C67639]">89.6% of Budget</p>
                        </div>
                    </div>
                    
                </div>

                <div class="flex justify-between items-center">
                    <p class="text-[27.5px] text-[#C67639] font-semibold">PKR 23929.0M</p>
                    <img src="./assets/arrow-right-brown.svg" class="cursor-pointer">
                </div>
                
            </div>

            <div class="bg-[#E1CEFF] w-[326px] h-[162px] rounded-[13px] p-5 flex flex-col justify-between w-full">

                <div class="flex items-center gap-3 border-b border-[#7848AB] pb-3">

                    <div class="flex justify-center items-center rounded-full w-[47px] h-[47px] border border-[#7848AB]">
                        <img src="./assets/empty-wallet-purple.svg" alt="">
                    </div>

                    <div class="flex flex-col gap-1">
                        <h3 class="text-[17px] text-[#7848AB] font-semibold">Total Releases</h3>
                        <div class="flex gap-2 items-center">
                            <img src="./assets/export-purple.svg" alt="">
                            <p class="text-[15px] text-[#7848AB]">39.1% Released</p>
                        </div>
                    </div>
                    
                </div>

                <div class="flex justify-between items-center">
                    <p class="text-[27.5px] text-[#7848AB] font-semibold">PKR 8375.1M</p>
                    <img src="./assets/arrow-right-purple.svg" class="cursor-pointer">
                </div>
                
            </div>

            <div class="bg-[#FFC8C9] w-[326px] h-[162px] rounded-[13px] p-5 flex flex-col justify-between w-full">

                <div class="flex items-center gap-3 border-b border-[#C74646] pb-3">

                    <div class="flex justify-center items-center rounded-full w-[47px] h-[47px] border border-[#C74646]">
                        <img src="./assets/empty-wallet-red.svg" alt="">
                    </div>

                    <div class="flex flex-col gap-1">
                        <h3 class="text-[17px] text-[#C74646] font-semibold">Expenditure</h3>
                        <div class="flex gap-2 items-center">
                            <img src="./assets/export-red.svg" alt="">
                            <p class="text-[15px] text-[#C74646]">29.3% Utilized</p>
                        </div>
                    </div>
                    
                </div>

                <div class="flex justify-between items-center">
                    <p class="text-[27.5px] text-[#C74646] font-semibold">PKR 2456.7M</p>
                    <img src="./assets/arrow-right-red.svg" class="cursor-pointer">
                </div>
                
            </div>


        </div>
    </div>

    <div class="p-5 flex gap-5">

        
        <div class="w-[30%] bg-white p-4 rounded-md">

            <div class="pb-3 border-b border-[#0D163A]">
                <p class="text-[#0D163A] font-bold text-[25px]">Dept-wise Allocation</p>
            </div>

            <div id="donut" class="mt-6"></div>
                
        </div>
        
        
        <div class="w-[70%] bg-white p-4 rounded-md">

            <div class="w-full">
                <p class="text-[#0D163A] font-bold text-[25px]">Budget vs Expenditure</p>
                <div class="w-full flex justify-end">

                    <div class="flex gap-4 ">
                        <div class="flex gap-3 items-center">
                            <div class="h-[14px] w-[14px] rounded-full bg-[#7DD587]"></div>
                            <p>Budget</p>
                        </div>
                        <div class="flex gap-3 items-center">
                            <div class="h-[14px] w-[14px] rounded-full bg-[#E8635E]"></div>
                            <p>Expenditure</p>
                        </div>
                        <select name="" id="" class="w-40 bg-[#EBF3F8] rounded-full">
                            <option value="" disabled selected>Quarterly</option>
                            <option value="">Option 1</option>
                            <option value="">Option 2</option>
                            <option value="">Option 3</option>
                        </select>
                    </div>
                    
                </div>
            </div>
            <div id="chart" class="w-full"></div>
        </div>

    </div>

    
    
    <div class="p-5 flex flex-col gap-10">
        <div class=" w-full bg-white rounded-md p-5 flex gap-10">

            <div class="w-[50%] flex flex-col justify-between gap-5">
                <div>
                    <p class="text-[#0D163A] text-[20px] font-semibold">Smart Office</p>

                    <p class="text-[#0D163A] text-[16px] mt-5">Federal Ministries & Departments PSDP through MoITT (NITB)</p>
                </div>

                <div class="w-full">
                    <table class="w-full border-separate border-spacing-y-[15px]">
                        <thead class="text-left">
                            <th>Total Budget</th>
                            <th>Allocated</th>
                            <th>Expenditure</th>
                            <th>Cumulative Exp</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Rs. 572.80 M</td>
                                <td>Rs. 300.00 M</td>
                                <td>Rs. 177.86 M</td>
                                <td>Rs. 281.41 M</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

            </div>

            
            <div class="w-[50%]">
                <div class="w-full bg-[#EBF3F8] p-[20px] rounded-[18px] h-full">
                    <div class="flex justify-between items-center pb-5 border-b border-[#B6B6B6]">
                        <div class="flex items-center gap-3">
                            <img src="./assets/coin-icon.svg" alt="">
                            <p class="text-[18px] font-semibold">Project Status</p>
                        </div>

                        <div class="flex items-center gap-3">

                            <img src="./assets/arrow-chart2.svg" class="cursor-pointer">

                            <p class="text-[10px] font-semibold">View Details</p>

                        </div>
                        
                    </div>

                    <div class="flex justify-between items-center mt-5">

                    <div class="flex flex-col gap-3">
                        <p class="text-[10px] font-semibold">Utilization against amount released</p>
                        <button class="cursor-pointer bg-[#1A92AA] text-white px-8 py-3 rounded-full text-[18px] font-semibold">61.5% Utilization</button>
                    </div>

                    <div id="chart2" class="w-[112px] h-[60px]"></div>
                    
                    </div>
                    
                </div>
            </div>
            
        </div>



        <div class=" w-full bg-white rounded-md p-5 flex gap-10">

            <div class="w-[50%] flex flex-col justify-between gap-5">
                <div>
                    <p class="text-[#0D163A] text-[20px] font-semibold">One Patient One ID</p>

                    <p class="text-[#0D163A] text-[16px] mt-5">Federal Ministries & Departments PSDP through MoITT (NITB)</p>
                </div>

                <div class="w-full">
                    <table class="w-full border-separate border-spacing-y-[15px]">
                        <thead class="text-left">
                            <th>Total Budget</th>
                            <th>Allocated</th>
                            <th>Expenditure</th>
                            <th>Cumulative Exp</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Rs. 572.80 M</td>
                                <td>Rs. 300.00 M</td>
                                <td>Rs. 177.86 M</td>
                                <td>Rs. 281.41 M</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

            </div>

            
            <div class="w-[50%]">
                <div class="w-full bg-[#EBF3F8] p-[20px] rounded-[18px] h-full">
                    <div class="flex justify-between items-center pb-5 border-b border-[#B6B6B6]">
                        <div class="flex items-center gap-3">
                            <img src="./assets/coin-icon.svg" alt="">
                            <p class="text-[18px] font-semibold">Project Status</p>
                        </div>

                        <div class="flex items-center gap-3">

                            <img src="./assets/arrow-chart2.svg" class="cursor-pointer">

                            <p class="text-[10px] font-semibold">View Details</p>

                        </div>
                        
                    </div>

                    <div class="flex justify-between items-center mt-5">

                    <div class="flex flex-col gap-3">
                        <p class="text-[10px] font-semibold">Utilization against amount released</p>
                        <button class="cursor-pointer bg-[#1A92AA] text-white px-8 py-3 rounded-full text-[18px] font-semibold">58% Utilization</button>
                    </div>

                    <div id="chart4" class="w-[112px] h-[60px]"></div>
                    
                    </div>
                    
                </div>
            </div>
            
        </div>
    </div>



<script>
// First chart (Budget vs Expenditure)
var options1 = {
    series: [{
        name: 'Budget',
        data: [31, 40, 28, 51]
    }, {
        name: 'Expenditure',
        data: [11, 32, 45, 32]
    }],
    legend: {
        show: false,
        labels: {
            show: false
        }
    },
    chart: {
        height: 350,
        type: 'area'
    },
    colors: ['#6AA487', '#E8635E'],
    dataLabels: {
        enabled: false
    },
    stroke: {
        curve: 'smooth'
    },
    xaxis: {
        categories: ["Q 1", "Q 2", "Q 3", "Q 4"],
    },
    yaxis: {
        show: false
    }
};

var chart1 = new ApexCharts(document.querySelector("#chart"), options1);
chart1.render();
</script>

<script>
// Second chart (chart2) - without numbering at breakpoints
const options2 = {
    series: [{
        name: 'Data Series',
        data: [30, 45, 39, 55]
    }],
    chart: {
        type: 'area',
        height: 100,
        toolbar: {
            show: false
        },
        zoom: {
            enabled: false
        }
    },
    stroke: {
        curve: 'smooth',
        width: 3,
        colors: ['#14B8A6']
    },
    fill: {
        type: 'gradient',
        gradient: {
            shade: 'light',
            type: 'vertical',
            shadeIntensity: 0.5,
            gradientToColors: ['transparent'],
            inverseColors: false,
            opacityFrom: 0.4,
            opacityTo: 0,
            stops: [0, 100]
        }
    },
    colors: ['#14B8A6'],
    grid: {
        show: false
    },
    xaxis: {
        labels: {
            show: false
        },
        axisBorder: {
            show: false
        },
        axisTicks: {
            show: false
        },
        categories: [1, 2, 3] // Hidden categories for data points
    },
    yaxis: {
        labels: {
            show: false
        }
    },
    tooltip: {
        enabled: true // Disabled tooltip to remove any numbering on hover
    },
    markers: {
        size: 0
    },
    dataLabels: {
        enabled: false // Ensures no data labels are shown
    }
};

const chart2 = new ApexCharts(document.querySelector("#chart2"), options2);
chart2.render();
</script>

<script>
// Donut chart
var options3 = {
    series: [44, 55, 41],
    chart: {
        type: 'donut',
    },
    colors: ['#75C9D9', '#FFD388', '#B085DD'],
    labels: ['Smart Office', '1P1ID', 'DEEP'],
    legend: {
        show: true,
        position: 'bottom',
    },
    responsive: [{
        breakpoint: 480,
        options: {
            chart: {
                width: 200
            },
            legend: {
                position: 'bottom'
            }
        }
    }]
};

var chart3 = new ApexCharts(document.querySelector("#donut"), options3);
chart3.render();
</script>



<script>
// Fourth chart (chart4) - same as chart2
const options4 = {
    series: [{
        name: 'Data Series',
        data: [30, 45, 39, 55]
    }],
    chart: {
        type: 'area',
        height: 100,
        toolbar: {
            show: false
        },
        zoom: {
            enabled: false
        }
    },
    stroke: {
        curve: 'smooth',
        width: 3,
        colors: ['#14B8A6']
    },
    fill: {
        type: 'gradient',
        gradient: {
            shade: 'light',
            type: 'vertical',
            shadeIntensity: 0.5,
            gradientToColors: ['transparent'],
            inverseColors: false,
            opacityFrom: 0.4,
            opacityTo: 0,
            stops: [0, 100]
        }
    },
    colors: ['#14B8A6'],
    grid: {
        show: false
    },
    xaxis: {
        labels: {
            show: false
        },
        axisBorder: {
            show: false
        },
        axisTicks: {
            show: false
        },
        categories: [1, 2, 3, 4] // Updated to match 4 data points
    },
    yaxis: {
        labels: {
            show: false
        }
    },
    tooltip: {
        enabled: true
    },
    markers: {
        size: 0
    },
    dataLabels: {
        enabled: false
    }
};

const chart4 = new ApexCharts(document.querySelector("#chart4"), options4);
chart4.render();
</script>
    
</body>
</html>