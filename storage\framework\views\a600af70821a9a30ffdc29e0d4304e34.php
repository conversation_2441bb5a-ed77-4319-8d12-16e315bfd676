<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>PSDP Management Dashboard</title>
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center">

    <!-- Left: Sign In -->
    <div class="w-1/2 p-10 space-y-6">
        <div class="flex items-center space-x-3">
            <img src="<?php echo e(asset('assets/moi.png')); ?>" class="w-10 h-10" alt="logo">
            <h2 class="text-lg font-semibold text-gray-800">Ministry of IT & Telecom</h2>
        </div>

        <h1 class="text-3xl font-bold text-gray-900">Sign in</h1>
        <p class="text-sm text-gray-600">Don't have an account? <a href="#" class="text-[#1A92AA] underline">Create now</a></p>

        <?php if($errors->any()): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                <ul class="list-disc list-inside">
                    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li><?php echo e($error); ?></li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            </div>
        <?php endif; ?>

        <?php if(session('success')): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                <?php echo e(session('success')); ?>

            </div>
        <?php endif; ?>

        <form method="POST" action="<?php echo e(route('login.post')); ?>" class="space-y-5">
            <?php echo csrf_field(); ?>
            <div>
                <label class="block text-sm text-gray-700 mb-1">E-mail</label>
                <input type="email" name="email" value="<?php echo e(old('email')); ?>" required
                       placeholder="<EMAIL>"
                       class="w-full px-4 py-2 text-sm border rounded-md focus:outline-none focus:ring-2 focus:ring-cyan-600 <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <div>
                <label class="block text-sm text-gray-700 mb-1">Password</label>
                <div class="relative">
                    <input type="password" name="password" required id="password"
                           class="w-full text-sm px-4 py-2 border rounded-md pr-10 focus:outline-none focus:ring-2 focus:ring-cyan-600 <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">

                    <!-- Vertical line -->
                    <span class="absolute right-10 top-1.5 h-6 w-[2px] bg-gray-300"></span>

                    <!-- Custom eye image -->
                    <img src="<?php echo e(asset('assets/eye.png')); ?>" alt="Toggle Password Visibility" class="absolute right-3 top-2.5 w-5 h-5 cursor-pointer" onclick="togglePassword()">
                </div>
                <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <div class="flex items-center justify-between text-sm">
                <label class="inline-flex items-center">
                    <input type="checkbox" name="remember" class="form-checkbox text-cyan-600">
                    <span class="ml-2 text-gray-600">Remember me</span>
                </label>
                <a href="#" class="text-[#1A92AA] underline">Forgot Password?</a>
            </div>

            <button type="submit" class="bg-cyan-600 hover:bg-cyan-700 text-white py-2 rounded-full font-semibold text-sm w-80 mx-auto block">
                Sign in
            </button>
        </form>

    </div>

    <!-- Right: Dashboard Illustration -->
    <div class="w-1/2 min-h-screen justify-center gap-12 bg-[url('<?php echo e(asset('assets/sign.png')); ?>')] bg-cover bg-center text-white p-10 flex flex-col text-center">
        <div class="w-full text-right">
            
        </div>

        <div class="mx-auto w-1/2">
            <img src="<?php echo e(asset('assets/main.png')); ?>" alt="dashboard illustration" class="max-w-full scale-110">
        </div>

        <h2 class="text-2xl font-bold mt-2">PSDP Management Dashboard</h2>
        <p class="text-sm max-w-md -mt-6 mx-auto">
            A smart tool to monitor fund allocation, spending, and financial progress of PSDP projects—ensuring transparency and efficient decision-making.
        </p>
    </div>

    <script>
        function togglePassword() {
            const passwordInput = document.getElementById('password');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
            } else {
                passwordInput.type = 'password';
            }
        }
    </script>
</body>
</html>
<?php /**PATH E:\laragon\www\psdb-dashboard-backend\resources\views/auth/login.blade.php ENDPATH**/ ?>