<?php

namespace App\Models\PSDP;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;

class UserMinistry extends Model
{
    protected $fillable = [
        'user_id',
        'ministry_id',
        'department_id',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean'
    ];

    /**
     * Get the user that owns the user ministry.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the ministry that owns the user ministry.
     */
    public function ministry(): BelongsTo
    {
        return $this->belongsTo(Ministry::class);
    }

    /**
     * Get the department that owns the user ministry.
     */
    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }
}
