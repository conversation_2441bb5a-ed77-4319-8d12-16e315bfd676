<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.jsdelivr.net/npm/flowbite@3.1.2/dist/flowbite.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script src="https://cdn.jsdelivr.net/npm/flowbite@3.1.2/dist/flowbite.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <title>MOITT</title>
</head>
<body class="bg-[#ebf3f8]">
    <?php include "./common/header.html" ?>

    <div class="p-5">
        <h2 class="text-[24px] text-[#0D163A] font-bold mt-2">PSDP Management Dashboard</h2>

        <div class="grid gap-5 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 mt-8">
        
            <div class="bg-[#AEE8FF] w-[326px] h-[162px] rounded-[13px] p-5 flex flex-col justify-between w-full">

                <div class="flex items-center gap-3 border-b border-[#40A5BA] pb-3">

                    <div class="flex justify-center items-center rounded-full w-[47px] h-[47px] border border-[#8FC3D2]">
                        <img src="./assets/empty-wallet.png" alt="">
                    </div>

                    <div class="flex flex-col gap-1">
                        <h3 class="text-[17px] text-[#1A92AA] font-semibold">Total Budget Allocation</h3>
                        <div class="flex gap-2 items-center">
                            <img src="./assets/export.svg" alt="">
                            <p class="text-[15px] text-[#1A92AA]">100% of Total Budget</p>
                        </div>
                    </div>
                    
                </div>

                <div class="flex justify-between items-center">
                    <p class="text-[27.5px] text-[#1A92AA] font-semibold">PKR 23929.0M</p>
                    <img src="./assets/arrow-right.svg" class="cursor-pointer">
                </div>
                
            </div>

            <div class="bg-[#FFE3C8] w-[326px] h-[162px] rounded-[13px] p-5 flex flex-col justify-between w-full">

                <div class="flex items-center gap-3 border-b border-[#C67639] pb-3">

                    <div class="flex justify-center items-center rounded-full w-[47px] h-[47px] border border-[#C67639]">
                        <img src="./assets/empty-wallet-brown.svg" alt="">
                    </div>

                    <div class="flex flex-col gap-1">
                        <h3 class="text-[17px] text-[#C67639] font-semibold">Final Allocation</h3>
                        <div class="flex gap-2 items-center">
                            <img src="./assets/export-brown.svg" alt="">
                            <p class="text-[15px] text-[#C67639]">89.6% of Budget</p>
                        </div>
                    </div>
                    
                </div>

                <div class="flex justify-between items-center">
                    <p class="text-[27.5px] text-[#C67639] font-semibold">PKR 23929.0M</p>
                    <img src="./assets/arrow-right-brown.svg" class="cursor-pointer">
                </div>
                
            </div>

            <div class="bg-[#E1CEFF] w-[326px] h-[162px] rounded-[13px] p-5 flex flex-col justify-between w-full">

                <div class="flex items-center gap-3 border-b border-[#7848AB] pb-3">

                    <div class="flex justify-center items-center rounded-full w-[47px] h-[47px] border border-[#7848AB]">
                        <img src="./assets/empty-wallet-purple.svg" alt="">
                    </div>

                    <div class="flex flex-col gap-1">
                        <h3 class="text-[17px] text-[#7848AB] font-semibold">Total Releases</h3>
                        <div class="flex gap-2 items-center">
                            <img src="./assets/export-purple.svg" alt="">
                            <p class="text-[15px] text-[#7848AB]">39.1% Released</p>
                        </div>
                    </div>
                    
                </div>

                <div class="flex justify-between items-center">
                    <p class="text-[27.5px] text-[#7848AB] font-semibold">PKR 8375.1M</p>
                    <img src="./assets/arrow-right-purple.svg" class="cursor-pointer">
                </div>
                
            </div>

            <div class="bg-[#FFC8C9] w-[326px] h-[162px] rounded-[13px] p-5 flex flex-col justify-between w-full">

                <div class="flex items-center gap-3 border-b border-[#C74646] pb-3">

                    <div class="flex justify-center items-center rounded-full w-[47px] h-[47px] border border-[#C74646]">
                        <img src="./assets/empty-wallet-red.svg" alt="">
                    </div>

                    <div class="flex flex-col gap-1">
                        <h3 class="text-[17px] text-[#C74646] font-semibold">Expenditure</h3>
                        <div class="flex gap-2 items-center">
                            <img src="./assets/export-red.svg" alt="">
                            <p class="text-[15px] text-[#C74646]">29.3% Utilized</p>
                        </div>
                    </div>
                    
                </div>

                <div class="flex justify-between items-center">
                    <p class="text-[27.5px] text-[#C74646] font-semibold">PKR 2456.7M</p>
                    <img src="./assets/arrow-right-red.svg" class="cursor-pointer">
                </div>
                
            </div>


        </div>
    </div>

    <div class="p-5 flex gap-5">

        
        <div class="w-[30%] bg-white p-4 rounded-md">

            <div class="pb-3 border-b border-[#0D163A]">
                <p class="text-[#0D163A] font-bold text-[25px]">Dept-wise Allocation</p>
            </div>

            <div id="donut" class="mt-6"></div>
                
        </div>
        
        
        <div class="w-[70%] bg-white p-4 rounded-md">

            <div class="w-full">
                <p class="text-[#0D163A] font-bold text-[25px]">Budget vs Expenditure</p>
                <div class="w-full flex justify-end">

                    <div class="flex gap-4 ">
                        <div class="flex gap-3 items-center">
                            <div class="h-[14px] w-[14px] rounded-full bg-[#7DD587]"></div>
                            <p>Budget</p>
                        </div>
                        <div class="flex gap-3 items-center">
                            <div class="h-[14px] w-[14px] rounded-full bg-[#E8635E]"></div>
                            <p>Expenditure</p>
                        </div>
                        <select name="" id="" class="w-40 bg-[#EBF3F8] rounded-full">
                            <option value="" disabled selected>Quarterly</option>
                            <option value="">Option 1</option>
                            <option value="">Option 2</option>
                            <option value="">Option 3</option>
                        </select>
                    </div>
                    
                </div>
            </div>
            <div id="chart3" class="w-full"></div>
        </div>

    </div>

    
    
    <div class="p-5">
        <div class=" w-full bg-white rounded-md p-5">
            <div class="space-y-4">
            <!-- Search and Sort -->
            <div class="flex items-center space-x-4">
                <!-- Search input -->
                <div class="flex items-center bg-[#f1f8fc] rounded-full px-4 py-2 w-[80%]">
                <img src="./assets/search-normal.svg" alt="">
                <input
                    type="text"
                    placeholder="Search here..."
                    class="bg-transparent focus:outline-none outline-none focus:ring-0 border-0 w-full text-sm text-gray-700"
                />
                </div>
                <!-- Sort button -->
                 <div class="w-[20%]">
                <button class="bg-[#f1f8fc] w-full text-gray-500 text-sm rounded-full px-4 py-4 ">
                Sort by: <span class="font-semibold text-gray-700">Project</span>
                </button>
                </div>
            </div>

            <!-- Heading and Legend -->
            <div class="flex flex-wrap items-center justify-between">
                <!-- Heading -->
                <h2 class="!text-[24px] sm:text-base font-semibold text-[#0D163A]">
                Project-wise Financial Overview (in Millions PKR)
                </h2>
                <!-- Legend -->
                <div class="flex flex-wrap items-center space-x-4 mt-2 sm:mt-0">
                <!-- Total Budget -->
                <div class="flex items-center space-x-1">
                    <span class="w-3 h-3 rounded-full bg-green-400"></span>
                    <span class="text-xs text-gray-600">Total Budget</span>
                </div>
                <!-- Allocated -->
                <div class="flex items-center space-x-1">
                    <span class="w-3 h-3 rounded-full bg-teal-500"></span>
                    <span class="text-xs text-gray-600">Allocated</span>
                </div>
                <!-- Released -->
                <div class="flex items-center space-x-1">
                    <span class="w-3 h-3 rounded-full bg-yellow-400"></span>
                    <span class="text-xs text-gray-600">Released</span>
                </div>
                <!-- Expenditure -->
                <div class="flex items-center space-x-1">
                    <span class="w-3 h-3 rounded-full bg-rose-400"></span>
                    <span class="text-xs text-gray-600">Expenditure</span>
                </div>
                </div>
            </div>
            </div>

            <div id="chart" class="w-full mt-3"></div>
        </div>
    </div>


    <a href="budgetDashboard2.php">
        <button class="px-8 py-3 bg-[#1A92AA] text-white rounded-full mb-5 mx-auto block cursor-pointer">
        View PSDP Projects
        </button>
    </a>






        
    
    <script>

        // Hearing Type Status Chart
 
      var options = {
          series: [{
          name: 'Total Cost',
          data: [44, 40, 47, 36, 26, 38, 36, 28, 44]
        }, {
          name: 'Actual Exp (2024)',
          data: [41, 29, 49, 33, 27, 39, 19, 48, 49]
        }, {
          name: 'FY 24-25 Allocation',
          data: [35, 41, 36, 26, 45, 48, 25, 35, 48]
        },
        {
          name: 'Cumulative Exp (2025)',
          data: [48, 29, 48, 36, 25, 40, 29, 30, 41]
        }
      ],
          chart: {
          type: 'bar',
          height: 350
        },
        legend: {
          show: false
        },
        colors: ['#7CD47F', '#1A92AA', '#F9BA33', '#CE6E6E'],
        plotOptions: {
          bar: {
            horizontal: false,
            columnWidth: '50%',
            borderRadius: 5,
            borderRadiusApplication: 'end'
          },
        },
        dataLabels: {
          enabled: false
        },
        stroke: {
          show: true,
          width: 2,
          colors: ['transparent']
        },
        xaxis: {
          categories: ['Proj 1','Proj 2', 'Proj 3', 'Proj 4', 'Proj 5', 'Proj 6', 'Proj 7', 'Proj 8', 'Proj 9'],
        },

        
        yaxis: {
                labels: {
                    formatter: function (val) {
                        return val + 'M';
                    },
                    style: {
                        fontSize: '12px',
                        colors: '#6B7280'
                    }
                },
                min: 5,
                max: 50,
                tickAmount: 5
            },
        grid: {
                show: true,
                borderColor: '#E5E7EB',
                strokeDashArray: 3,
                xaxis: {
                    lines: {
                        show: false
                    }
                },
                yaxis: {
                    lines: {
                        show: true
                    }
                }
            },
        
        fill: {
          opacity: 1
        },
        
        };

        var chart = new ApexCharts(document.querySelector("#chart"), options);
        chart.render();
    </script>
    
    <script>

        // Hearing Type Status Chart
 
      var options = {
          series: [{
          name: 'Budget',
          data: [44, 40, 36, 48]
        }, {
          name: 'Expenditure',
          data: [48, 36, 47, 42]
        }, 
      ],
          chart: {
          type: 'bar',
          height: 350
        },
        colors: ['#7DD587', '#E8635E'],
        plotOptions: {
          bar: {
            horizontal: false,
            columnWidth: '50%',
            borderRadius: 5,
            borderRadiusApplication: 'end'
          },
        },
        dataLabels: {
          enabled: false
        },
        stroke: {
          show: true,
          width: 2,
          colors: ['transparent']
        },
        xaxis: {
          categories: ['Q 1','Q 2', 'Q 3', 'Q 4'],
        },

        yaxis: {
                labels: {
                    formatter: function (val) {
                        return val + 'K';
                    },
                    style: {
                        fontSize: '12px',
                        colors: '#6B7280'
                    }
                },
                min: 0,
                max: 50,
                tickAmount: 5
            },
        grid: {
                show: true,
                borderColor: '#E5E7EB',
                strokeDashArray: 3,
                xaxis: {
                    lines: {
                        show: false
                    }
                },
                yaxis: {
                    lines: {
                        show: true
                    }
                }
            },
        fill: {
          opacity: 1
        },
        
        };

        var chart = new ApexCharts(document.querySelector("#chart3"), options);
        chart.render();
    </script>






<script>

  // Order Status Chart

  
  var options = {
    series: [80, 60, 40], // percentages of completion
    chart: {
      height: 350,
      type: 'radialBar',
    },
    colors: ['#0987B9', '#59C7E7', '#203876'],
    plotOptions: {
      radialBar: {
        startAngle: 0,
        endAngle: 360,
        hollow: {
          margin: 15,
          size: '50%',
          background: 'transparent',
        },
        track: {
          background: '#F3F4F6',
          strokeWidth: '100%',
        },
        total: {
        show: true,
        label: '.',
        formatter: function (w) {
          return '';
        }
      },
      dataLabels: {
        name: {
          fontSize: '12px',
          offsetY: 0
        },
        value: {
          fontSize: '14px',
        },
        total: {
            show: true,
            label: 'Total Orders Received',
            fontSize: '12px',
            formatter: function () {
              return '40';
            }
          }
      }
     }
          },
          labels: ['Short Orders', 'Detailed Orders', 'Forwarded to Ombudsperson'],
        };

  var chart = new ApexCharts(document.querySelector("#chart2"), options);
  chart.render();
</script>

        <script>
          var options = {
                  series: [44, 55, 41, 17, 15],
                  chart: {
                  type: 'donut',
                },
          colors: ['#75C9D9', '#99E194', '#FFD388', '#F29797', '#B085DD'],
                labels: ['NITB', 'NTC', 'Ignite', 'USF', 'SCO'],
                legend: {
          show: true,
          position: 'bottom',
        },
        responsive: [{
          breakpoint: 480,
          options: {
            chart: {
              width: 200
            },
            legend: {
              position: 'bottom'
            }
          }
        }]
        };

        var chart = new ApexCharts(document.querySelector("#donut"), options);
        chart.render();

      </script>
    
</body>
</html>