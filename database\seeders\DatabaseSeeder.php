<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            RolePermissionSeeder::class,
            ApprovingAuthoritySeeder::class,
            StatusSeeder::class,
            MinistrySeeder::class,
            DivisionSeeder::class,
            DepartmentSeeder::class,
            FinancialYearSeeder::class,
            UserSeeder::class, // UserSeeder now includes UserMinistry records
        ]);
    }
}
