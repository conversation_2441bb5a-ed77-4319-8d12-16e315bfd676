@extends('layouts.app')

@section('title', 'Add Revision - ' . $project->name)

@section('content')
<div class="py-10 px-6">
    <div class="max-w-4xl mx-auto bg-white rounded-xl shadow p-5 sm:p-12 sm:px-16">
        <!-- Breadcrumb -->
        <div class="flex items-center gap-2 mb-6 text-sm text-gray-600">
            <a href="{{ route('psdp.pd-dashboard') }}" class="hover:text-blue-600">PSDP Projects</a>
            <span>></span>
            <a href="{{ route('psdp.pd-projects.index') }}" class="hover:text-blue-600">Projects</a>
            <span>></span>
            <a href="{{ route('psdp.pd-projects.show', $project->id) }}" class="hover:text-blue-600">{{ $project->name }}</a>
            <span>></span>
            <span class="text-gray-900">Add Revision</span>
        </div>

        <!-- Header -->
        <h2 class="text-xl sm:text-2xl font-semibold text-center text-gray-900 mb-8">
            Add Project Revision
        </h2>

        <!-- Project Info -->
        <div class="bg-gray-50 rounded-lg p-4 mb-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-2">Project Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div><strong>Project Name:</strong> {{ $project->name }}</div>
                <div><strong>PSDP ID:</strong> {{ $project->psdp_id }}</div>
                <div><strong>Current Total Cost:</strong> PKR {{ number_format($project->total_cost, 2) }}</div>
                <div><strong>Current Grand Amount:</strong> PKR {{ number_format($project->grand_amount ?? $project->total_cost, 2) }}</div>
            </div>
        </div>

        <!-- Revision Form -->
        <form id="revisionForm" method="POST" action="{{ route('psdp.pd-projects.revisions.store', $project->id) }}" novalidate>
            @csrf
            <input type="hidden" name="project_id" value="{{ $project->id }}">

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="relative mt-2 w-full mx-auto">
                    <input type="date" name="authority_approved_date" id="authority_approved_date"
                           class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0" />
                    <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                        Authority Approved Date
                    </label>
                </div>

                <div class="relative mt-2 w-full mx-auto">
                    <select name="approving_authority_id" id="approving_authority_id" required
                            class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0 appearance-none">
                        <option value="">Select approving authority</option>
                        @foreach($approvingAuthorities as $authority)
                            <option value="{{ $authority->id }}">{{ $authority->name }}</option>
                        @endforeach
                    </select>
                    <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                        Approving Authority*
                    </label>
                    <div class="pointer-events-none absolute inset-y-0 right-4 flex items-center text-gray-500">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                            <path d="M19 9l-7 7-7-7" />
                        </svg>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="relative mt-2 w-full mx-auto">
                    <input type="date" name="admin_approval_date" id="admin_approval_date"
                           class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0" />
                    <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                        Admin Approval Date
                    </label>
                </div>

                <div class="relative mt-2 w-full mx-auto">
                    <input type="date" name="revised_date" id="revised_date" required
                           class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0" />
                    <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                        Revised Date*
                    </label>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div class="relative mt-2 w-full mx-auto">
                    <input type="number" name="lc_amount" id="lc_amount" step="0.01" min="0"
                           class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                           placeholder="0.00" />
                    <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                        LC Amount (PKR)
                    </label>
                </div>

                <div class="relative mt-2 w-full mx-auto">
                    <input type="number" name="fc_amount" id="fc_amount" step="0.01" min="0"
                           class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                           placeholder="0.00" />
                    <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                        FC Amount (USD)
                    </label>
                </div>

                <div class="relative mt-2 w-full mx-auto">
                    <input type="number" name="additional_cost" id="additional_cost" step="0.01" min="0"
                           class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                           placeholder="0.00" />
                    <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                        Additional Cost (PKR)
                    </label>
                </div>
            </div>

            <!-- Cost Summary -->
            <div class="bg-blue-50 rounded-lg p-4 mb-6">
                <h4 class="text-md font-semibold text-gray-800 mb-2">Cost Summary</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div><strong>Current Grand Amount:</strong> PKR <span id="current-grand">{{ number_format($project->grand_amount ?? $project->total_cost, 2) }}</span></div>
                    <div><strong>Additional Cost:</strong> PKR <span id="additional-display">0.00</span></div>
                    <div><strong>New Grand Amount:</strong> PKR <span id="new-grand">{{ number_format($project->grand_amount ?? $project->total_cost, 2) }}</span></div>
                </div>
            </div>

            <!-- Navigation Buttons -->
            <div class="flex justify-center mt-8">
                <div class="flex flex-col sm:flex-row justify-center gap-4 w-full">
                    <a href="{{ route('psdp.pd-projects.show', $project->id) }}"
                       class="w-full sm:w-36 text-black text-sm font-medium rounded-full px-4 py-2.5 hover:brightness-105 text-center"
                       style="background-color: #D6DCE4;">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="w-full sm:w-36 text-white text-sm font-medium rounded-full px-4 py-2.5 hover:brightness-110" 
                            style="background-color: #1A92AA;">
                        Add Revision
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
$(document).ready(function() {
    const currentGrand = {{ $project->grand_amount ?? $project->total_cost }};
    
    // Update cost summary when additional cost changes
    $('#additional_cost').on('input', function() {
        const additionalCost = parseFloat($(this).val()) || 0;
        const newGrand = currentGrand + additionalCost;
        
        $('#additional-display').text(additionalCost.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}));
        $('#new-grand').text(newGrand.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}));
    });

    // Form validation and submission
    $('#revisionForm').submit(function(e) {
        e.preventDefault();

        // Clear previous errors
        $('.error-message').remove();
        $('.border-red-500').removeClass('border-red-500');

        // Validate required fields
        let isValid = true;
        $('input[required], select[required]').each(function() {
            if (!$(this).val()) {
                isValid = false;
                $(this).addClass('border-red-500');
                $(this).after('<span class="error-message text-red-500 text-xs mt-1">This field is required</span>');
            }
        });

        if (!isValid) {
            return;
        }

        // Submit via AJAX
        const formData = new FormData(this);

        $.ajax({
            url: $(this).attr('action'),
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    alert('Revision added successfully!');
                    window.location.href = response.redirect;
                }
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    const errors = xhr.responseJSON.errors;
                    Object.keys(errors).forEach(function(field) {
                        const input = $(`[name="${field}"]`);
                        input.addClass('border-red-500');
                        input.after(`<span class="error-message text-red-500 text-xs mt-1">${errors[field][0]}</span>`);
                    });
                } else {
                    alert('Error adding revision. Please try again.');
                }
            }
        });
    });
});
</script>
@endsection
