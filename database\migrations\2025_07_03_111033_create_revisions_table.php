<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('revisions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained('projects')->onDelete('cascade');
            $table->date('authority_approved_date')->nullable();
            $table->foreignId('approving_authority_id')->constrained('approving_authorities')->onDelete('cascade');
            $table->date('admin_approval_date')->nullable();
            $table->date('revised_date');
            $table->decimal('lc_amount', 15, 2)->nullable()->comment('Local Currency amount');
            $table->decimal('fc_amount', 15, 2)->nullable()->comment('Foreign Currency amount');
            $table->decimal('additional_cost', 15, 2)->nullable()->comment('Additional cost for this revision');
            $table->foreignId('added_by_user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('revisions');
    }
};
