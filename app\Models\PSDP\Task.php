<?php

namespace App\Models\PSDP;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Task extends Model
{
    protected $fillable = [
        'project_id',
        'deliverable_id',
        'code',
        'description',
        'start_date',
        'end_date',
        'planned_complete',
        'actual_complete',
        'status_id',
        'remarks',
        'is_active'
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'planned_complete' => 'decimal:2',
        'actual_complete' => 'decimal:2',
        'is_active' => 'boolean'
    ];

    /**
     * Get the project that owns the task.
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get the deliverable that owns the task.
     */
    public function deliverable(): BelongsTo
    {
        return $this->belongsTo(Deliverable::class);
    }

    /**
     * Get the status that owns the task.
     */
    public function status(): BelongsTo
    {
        return $this->belongsTo(Status::class);
    }
}
