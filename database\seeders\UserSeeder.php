<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\PSDP\UserMinistry;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Super Admin User (if not exists)
        $superAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Admin',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]
        );
        if (!$superAdmin->hasRole('super admin')) {
            $superAdmin->assignRole('super admin');
        }

        // Create Ministry User (if not exists)
        $ministry = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]
        );
        if (!$ministry->hasRole('admin')) {
            $ministry->assignRole('admin');
        }

        // Create PD (Project Director) User for NITB (if not exists)
        $pdNitb = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'PD NITB',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]
        );
        if (!$pdNitb->hasRole('PD')) {
            $pdNitb->assignRole('PD');
        }

        // Create PD (Project Director) User for PSEB (if not exists)
        $pdPseb = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'PD PSEB',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]
        );
        if (!$pdPseb->hasRole('PD')) {
            $pdPseb->assignRole('PD');
        }

        // Assign Ministry and Department to Ministry User (Admin)
        // Ministry of Information Technology and Telecommunication (ID: 16)
        // Can access all departments in the ministry
        UserMinistry::firstOrCreate([
            'user_id' => $ministry->id,
            'ministry_id' => 16,
        ], [
            'department_id' => null, // Can access all departments in the ministry
            'is_active' => true,
        ]);

        // Assign specific department to PD NITB User
        // National Information Technology Board (NITB) - Department ID: 198
        UserMinistry::firstOrCreate([
            'user_id' => $pdNitb->id,
            'ministry_id' => 16,
            'department_id' => 198,
        ], [
            'is_active' => true,
        ]);

        // Assign specific department to PD PSEB User
        // Pakistan Software Export Board (PSEB) - Department ID: 364
        UserMinistry::firstOrCreate([
            'user_id' => $pdPseb->id,
            'ministry_id' => 16,
            'department_id' => 364,
        ], [
            'is_active' => true,
        ]);
    }
}
