@extends('layouts.app')

@section('title', 'Project Details - ' . $project->name)

@section('content')
<div class="py-10 px-6">
    <div class="max-w-7xl mx-auto">
        <!-- Breadcrumb -->
        <div class="flex items-center gap-2 mb-6 text-sm text-gray-600">
            <a href="{{ route('psdp.pd-dashboard') }}" class="hover:text-blue-600">PSDP Projects</a>
            <span>></span>
            <a href="{{ route('psdp.pd-projects.index') }}" class="hover:text-blue-600">Projects</a>
            <span>></span>
            <span class="text-gray-900">{{ $project->name }}</span>
        </div>

        <!-- Header -->
        <div class="flex justify-between items-start mb-8">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ $project->name }}</h1>
                <p class="text-gray-600">PSDP ID: {{ $project->psdp_id }}</p>
            </div>
            <div class="flex gap-3">
                <a href="{{ route('psdp.pd-projects.revisions.create', $project->id) }}"
                   class="bg-green-600 text-white px-6 py-3 rounded-full font-medium hover:brightness-110">
                    Add Revision
                </a>
                <a href="{{ route('psdp.pd-projects.edit', $project->id) }}"
                   class="bg-[#1A92AA] text-white px-6 py-3 rounded-full font-medium hover:brightness-110">
                    Edit Project
                </a>
                <a href="{{ route('psdp.pd-projects.index') }}"
                   class="bg-gray-200 text-gray-700 px-6 py-3 rounded-full font-medium hover:bg-gray-300">
                    Back to Projects
                </a>
            </div>
        </div>

        <!-- Project Information -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <div class="lg:col-span-2">
                <div class="bg-white rounded-xl shadow p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Project Information</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="text-sm font-medium text-gray-600">Department</label>
                            <p class="text-gray-900">{{ $project->department->name }}</p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-gray-600">Approving Authority</label>
                            <p class="text-gray-900">{{ $project->approvingAuthority->name }}</p>
                        </div>
                        @if($project->authority_approved_date)
                        <div>
                            <label class="text-sm font-medium text-gray-600">Authority Approved Date</label>
                            <p class="text-gray-900">{{ $project->authority_approved_date->format('M d, Y') }}</p>
                        </div>
                        @endif
                        @if($project->admin_approval_date)
                        <div>
                            <label class="text-sm font-medium text-gray-600">Admin Approval Date</label>
                            <p class="text-gray-900">{{ $project->admin_approval_date->format('M d, Y') }}</p>
                        </div>
                        @endif
                        @if($project->completion_date_pc1)
                        <div>
                            <label class="text-sm font-medium text-gray-600">Completion Date PC1</label>
                            <p class="text-gray-900">{{ $project->completion_date_pc1->format('M d, Y') }}</p>
                        </div>
                        @endif
                        @if($project->likely_completion_date)
                        <div>
                            <label class="text-sm font-medium text-gray-600">Likely Completion Date</label>
                            <p class="text-gray-900">{{ $project->likely_completion_date->format('M d, Y') }}</p>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <div>
                <div class="bg-white rounded-xl shadow p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Project Stats</h2>
                    <div class="space-y-4">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Total Deliverables</span>
                            <span class="font-semibold">{{ $project->deliverables->count() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Total Tasks</span>
                            <span class="font-semibold">{{ $project->tasks->count() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Completed Tasks</span>
                            <span class="font-semibold">{{ $project->tasks->where('actual_complete', 100)->count() }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Project Revisions -->
        @if($project->revisions->count() > 0)
        <div class="bg-white rounded-xl shadow p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-6">Project Revisions</h2>

            <div class="overflow-x-auto">
                <table class="w-full border-collapse border border-gray-300 rounded-lg">
                    <thead>
                        <tr class="bg-gray-50">
                            <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Revised Date</th>
                            <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Approving Authority</th>
                            <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Additional Cost</th>
                            <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Added By</th>
                            <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($project->revisions->sortByDesc('revised_date') as $revision)
                        <tr class="hover:bg-gray-50">
                            <td class="border border-gray-300 px-4 py-3 text-sm text-gray-700">
                                {{ $revision->revised_date->format('M d, Y') }}
                            </td>
                            <td class="border border-gray-300 px-4 py-3 text-sm text-gray-700">
                                {{ $revision->approvingAuthority->name }}
                            </td>
                            <td class="border border-gray-300 px-4 py-3 text-sm text-gray-700">
                                {{ number_format($revision->additional_cost / 1000000, 3) }} Million PKR
                            </td>
                            <td class="border border-gray-300 px-4 py-3 text-sm text-gray-700">
                                {{ $revision->addedByUser->name ?? 'N/A' }}
                            </td>
                            <td class="border border-gray-300 px-4 py-3">
                                <div class="flex space-x-3">
                                    <a href="{{ route('psdp.pd-projects.revisions.show', [$project->id, $revision->id]) }}"
                                       class="text-blue-600 hover:text-blue-800 p-1 rounded-full hover:bg-blue-50"
                                       title="View Revision">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                        </svg>
                                    </a>
                                    <a href="{{ route('psdp.pd-projects.revisions.edit', [$project->id, $revision->id]) }}"
                                       class="text-green-600 hover:text-green-800 p-1 rounded-full hover:bg-green-50"
                                       title="Edit Revision">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                        </svg>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
        @endif

        <!-- Deliverables and Tasks -->
        <div class="bg-white rounded-xl shadow p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-6">Deliverables & Tasks</h2>
            
            @forelse($project->deliverables as $deliverable)
            <div class="border rounded-lg p-4 mb-6">
                <h3 class="text-md font-semibold text-gray-800 mb-4">{{ $deliverable->name }}</h3>
                
                @if($deliverable->tasks->count() > 0)
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Code</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Description</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Start Date</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">End Date</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Progress</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($deliverable->tasks as $task)
                            <tr>
                                <td class="px-4 py-2 text-sm text-gray-900">{{ $task->code }}</td>
                                <td class="px-4 py-2 text-sm text-gray-900">{{ $task->description }}</td>
                                <td class="px-4 py-2 text-sm text-gray-900">{{ $task->start_date->format('M d, Y') }}</td>
                                <td class="px-4 py-2 text-sm text-gray-900">{{ $task->end_date->format('M d, Y') }}</td>
                                <td class="px-4 py-2 text-sm text-gray-900">
                                    <div class="flex items-center">
                                        <div class="w-full bg-gray-200 rounded-full h-2 mr-2">
                                            <div class="bg-[#1A92AA] h-2 rounded-full" style="width: {{ $task->actual_complete }}%"></div>
                                        </div>
                                        <span class="text-xs">{{ $task->actual_complete }}%</span>
                                    </div>
                                </td>
                                <td class="px-4 py-2 text-sm">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                        @if($task->status->name == 'Completed') bg-green-100 text-green-800
                                        @elseif($task->status->name == 'Delayed') bg-red-100 text-red-800
                                        @elseif($task->status->name == 'On-track') bg-blue-100 text-blue-800
                                        @else bg-gray-100 text-gray-800 @endif">
                                        {{ $task->status->name }}
                                    </span>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                @else
                <p class="text-gray-500 text-center py-4">No tasks defined for this deliverable.</p>
                @endif
            </div>
            @empty
            <div class="text-center py-8">
                <img src="{{ asset('assets/empty-wallet.png') }}" alt="" class="mx-auto w-16 h-16 mb-4 opacity-50">
                <p class="text-gray-500">No deliverables defined for this project.</p>
            </div>
            @endforelse
        </div>
    </div>
</div>
@endsection
