<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PSDP\Division;

class DivisionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $divisions = [
            ['id' => 1, 'ministry_id' => 1, 'name' => 'Aviation Division', 'isActive' => null],
            ['id' => 2, 'ministry_id' => 1, 'name' => 'Cabinet Division', 'isActive' => null],
            ['id' => 3, 'ministry_id' => 1, 'name' => 'Establishment Division', 'isActive' => null],
            ['id' => 4, 'ministry_id' => 1, 'name' => 'National Security Division', 'isActive' => null],
            ['id' => 5, 'ministry_id' => 1, 'name' => 'Poverty Alleviation & Social Safety Division', 'isActive' => null],
            ['id' => 6, 'ministry_id' => 2, 'name' => 'Climate Change Division', 'isActive' => null],
            ['id' => 7, 'ministry_id' => 3, 'name' => 'Commerce Division', 'isActive' => null],
            ['id' => 8, 'ministry_id' => 4, 'name' => 'Communication Division', 'isActive' => null],
            ['id' => 9, 'ministry_id' => 5, 'name' => 'Defence Division', 'isActive' => null],
            ['id' => 10, 'ministry_id' => 6, 'name' => 'Defence Production Division', 'isActive' => null],
            ['id' => 11, 'ministry_id' => 7, 'name' => 'Economic Affairs Division', 'isActive' => null],
            ['id' => 12, 'ministry_id' => 8, 'name' => 'Power Division', 'isActive' => null],
            ['id' => 13, 'ministry_id' => 8, 'name' => 'Petroleum Division', 'isActive' => null],
            ['id' => 14, 'ministry_id' => 9, 'name' => 'Federal Education and Professional Training Division', 'isActive' => null],
            ['id' => 15, 'ministry_id' => 9, 'name' => 'National Heritage & Cultural Division', 'isActive' => null],
            ['id' => 16, 'ministry_id' => 10, 'name' => 'Finance Division', 'isActive' => null],
            ['id' => 17, 'ministry_id' => 10, 'name' => 'Revenue Division', 'isActive' => null],
            ['id' => 18, 'ministry_id' => 11, 'name' => 'Foreign Affairs Division', 'isActive' => null],
            ['id' => 19, 'ministry_id' => 12, 'name' => 'Housing & Works Division', 'isActive' => null],
            ['id' => 20, 'ministry_id' => 13, 'name' => 'Human Rights Division', 'isActive' => null],
            ['id' => 21, 'ministry_id' => 14, 'name' => 'Industries & Production Division', 'isActive' => null],
            ['id' => 22, 'ministry_id' => 15, 'name' => 'Information & Broadcasting Division', 'isActive' => null],
            ['id' => 23, 'ministry_id' => 16, 'name' => 'Information Technology & Telecommunication Division', 'isActive' => null],
            ['id' => 24, 'ministry_id' => 17, 'name' => 'Interior Division', 'isActive' => null],
            ['id' => 25, 'ministry_id' => 18, 'name' => 'Inter Provincial & Coordination Division', 'isActive' => null],
            ['id' => 26, 'ministry_id' => 19, 'name' => 'Kashmir Affairs and Gilgit Baltistan Division', 'isActive' => null],
            ['id' => 27, 'ministry_id' => 20, 'name' => 'Law & Justice Division', 'isActive' => null],
            ['id' => 28, 'ministry_id' => 21, 'name' => 'Maritime Affairs Division', 'isActive' => null],
            ['id' => 29, 'ministry_id' => 22, 'name' => 'Narcotics Control Division', 'isActive' => null],
            ['id' => 30, 'ministry_id' => 23, 'name' => 'National Food Security and Research Division', 'isActive' => null],
            ['id' => 31, 'ministry_id' => 24, 'name' => 'National Health Services Regulations and Coordination Division', 'isActive' => null],
            ['id' => 32, 'ministry_id' => 25, 'name' => 'Overseas Pakistanis and Human Resource Development Division', 'isActive' => null],
            ['id' => 33, 'ministry_id' => 26, 'name' => 'Parliamentary Affairs Division', 'isActive' => null],
            ['id' => 34, 'ministry_id' => 27, 'name' => 'Planning Development & Special Initiatives Division', 'isActive' => null],
            ['id' => 35, 'ministry_id' => 28, 'name' => 'Privitization Division', 'isActive' => null],
            ['id' => 36, 'ministry_id' => 29, 'name' => 'Railways Division', 'isActive' => null],
            ['id' => 37, 'ministry_id' => 30, 'name' => 'Religious Affairs and Inter-Faith Harmony Division', 'isActive' => null],
            ['id' => 38, 'ministry_id' => 31, 'name' => 'Science & Technology Division', 'isActive' => null],
            ['id' => 39, 'ministry_id' => 32, 'name' => 'States and Frontier Regions Division', 'isActive' => null],
            ['id' => 40, 'ministry_id' => 33, 'name' => 'Water Resources Division', 'isActive' => null],
            ['id' => 41, 'ministry_id' => 34, 'name' => 'Prime Minister Office', 'isActive' => null],
            ['id' => 42, 'ministry_id' => 34, 'name' => 'Miscellaneous', 'isActive' => null],
            ['id' => 43, 'ministry_id' => 35, 'name' => 'President Secretariat', 'isActive' => null],
        ];

        foreach ($divisions as $division) {
            Division::updateOrCreate(
                ['id' => $division['id']],
                $division
            );
        }
    }
}
