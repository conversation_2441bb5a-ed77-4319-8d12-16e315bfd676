<?php

namespace App\Models\PSDP;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;

class FinancialYear extends Model
{
    protected $fillable = [
        'name',
        'no_of_revisions',
        'last_revised_date',
        'added_by_user_id',
        'is_active'
    ];

    protected $casts = [
        'last_revised_date' => 'date',
        'is_active' => 'boolean'
    ];

    /**
     * Get the user who added this financial year.
     */
    public function addedByUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'added_by_user_id');
    }

    /**
     * Get the allocations for this financial year.
     */
    public function allocations(): HasMany
    {
        return $this->hasMany(Allocation::class);
    }
}
