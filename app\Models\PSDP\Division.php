<?php

namespace App\Models\PSDP;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Division extends Model
{
    protected $fillable = [
        'ministry_id',
        'name',
        'isActive'
    ];

    protected $casts = [
        'isActive' => 'boolean'
    ];

    /**
     * Get the ministry that owns the division.
     */
    public function ministry(): BelongsTo
    {
        return $this->belongsTo(Ministry::class, 'ministry_id');
    }

    /**
     * Get the departments for this division.
     */
    public function departments(): HasMany
    {
        return $this->hasMany(Department::class, 'division_id');
    }
}
