<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserMinistry extends Model
{
    protected $table = 'user_ministries';

    protected $fillable = [
        'user_id',
        'ministry_id'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function ministry()
    {
        return $this->belongsTo(\App\Models\PSDP\Ministry::class);
    }
}
