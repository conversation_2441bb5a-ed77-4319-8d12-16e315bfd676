# PSDP Dashboard Backend

A Laravel-based PSDP (Public Sector Development Program) project management system with a 4-step project creation form and comprehensive dashboard.

## Features

- **Custom Authentication System**: Based on the provided signIn.html design
- **Role-based Access Control**: Using Spatie <PERSON>vel Permission (Super Admin, Admin, PD roles)
- **4-Step Project Creation Form**: 
  1. Project Information
  2. Project Timeline
  3. Budget Allocations
  4. Deliverables & Tasks
- **Comprehensive Dashboard**: Project statistics and quick actions
- **Project Management**: Full CRUD operations for projects
- **Responsive Design**: Using Tailwind CSS

## Database Structure

### Core Tables
- `users` - System users with role-based permissions
- `projects` - Main project information
- `departments` - Government departments (Ignite, NITB, NTC, SCO, USF)
- `approving_authorities` - DDWP, CDWP, ECNEC
- `statuses` - Task statuses (Completed, Delayed, Not Started, Behind, On-track)
- `financial_years` - Financial year management
- `allocations` - Budget allocations per project/financial year
- `quarters` - Quarterly financial data
- `deliverables` - Project deliverables
- `tasks` - Individual tasks with progress tracking

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   composer install
   ```

3. Set up environment:
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

4. Configure database in `.env` file

5. Run migrations and seeders:
   ```bash
   php artisan migrate:fresh --seed
   ```

6. Start the development server:
   ```bash
   php artisan serve
   ```

## Default Login Credentials

- **Email**: <EMAIL>
- **Password**: password
- **Role**: Super Admin

## API Endpoints

### Authentication
- `GET /login` - Show login form
- `POST /login` - Authenticate user
- `POST /logout` - Logout user

### PSDP Routes (Protected)
- `GET /psdp/dashboard` - Main dashboard
- `GET /psdp/projects` - List all projects
- `GET /psdp/projects/create` - Show 4-step project creation form
- `POST /psdp/projects` - Store new project
- `GET /psdp/projects/{id}` - Show project details
- `GET /psdp/projects/{id}/edit` - Edit project
- `PUT /psdp/projects/{id}` - Update project
- `DELETE /psdp/projects/{id}` - Delete project

## Project Structure

```
app/
├── Http/Controllers/
│   ├── Auth/AuthController.php
│   └── PSDP/
│       ├── DashboardController.php
│       └── ProjectController.php
├── Models/PSDP/
│   ├── Project.php
│   ├── Department.php
│   ├── ApprovingAuthority.php
│   ├── Status.php
│   ├── FinancialYear.php
│   ├── Allocation.php
│   ├── Quarter.php
│   ├── Deliverable.php
│   └── Task.php
resources/views/
├── layouts/
│   ├── app.blade.php
│   └── header.blade.php
├── auth/
│   └── login.blade.php
└── psdp/
    ├── dashboard.blade.php
    └── projects/
        ├── index.blade.php
        ├── create.blade.php
        ├── show.blade.php
        └── edit.blade.php
```

## Key Features

### 4-Step Project Creation Form
1. **Project Information**: Basic project details, department, PSDP ID, approving authority
2. **Project Timeline**: Important dates and milestones
3. **Budget Allocations**: Financial allocations across different financial years
4. **Deliverables & Tasks**: Project deliverables with associated tasks, progress tracking

### Dashboard Features
- Project statistics by status and department
- Quick action buttons
- Recent project activities
- Financial overview

### Role-based Permissions
- **Super Admin**: Full system access
- **Admin**: Project management and reporting
- **PD (Project Director)**: Limited project access

## Technologies Used

- **Backend**: Laravel 11
- **Frontend**: Blade Templates + Tailwind CSS
- **Database**: MySQL/SQLite
- **Authentication**: Custom Laravel Auth
- **Permissions**: Spatie Laravel Permission
- **JavaScript**: jQuery for form interactions

## System Requirements

- PHP 8.2+
- Composer
- MySQL 8.0+ or SQLite
- Node.js (for asset compilation if needed)

## Testing

To test the system:

1. Access the application at `http://127.0.0.1:8000`
2. Login with the default credentials
3. Navigate through the dashboard
4. Create a new project using the 4-step form
5. View and manage existing projects

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

This project is proprietary software for the Ministry of IT & Telecom, Government of Pakistan.
