<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\PSDP\Ministry;
use App\Models\PSDP\Department;
use App\Models\PSDP\UserMinistry;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;

class UserController extends Controller
{
    /**
     * Display a listing of users based on role
     */
    public function index()
    {
        $user = auth()->user();
        
        if ($user->hasRole('super admin')) {
            // Super admin can see all users
            $users = User::with(['roles', 'userMinistries.ministry', 'userMinistries.department'])
                ->orderBy('created_at', 'desc')
                ->paginate(15);
        } elseif ($user->hasRole('admin')) {
            // Ministry admin can see only PD users from their ministry
            $userMinistry = $user->userMinistries()->first();
            if ($userMinistry) {
                $users = User::whereHas('userMinistries', function($query) use ($userMinistry) {
                    $query->where('ministry_id', $userMinistry->ministry_id);
                })
                ->whereHas('roles', function($query) {
                    $query->where('name', 'PD');
                })
                ->with(['roles', 'userMinistries.ministry', 'userMinistries.department'])
                ->orderBy('created_at', 'desc')
                ->paginate(15);
            } else {
                $users = collect();
            }
        } else {
            // PD users cannot manage other users
            abort(403, 'Unauthorized access');
        }

        return view('users.index', compact('users'));
    }

    /**
     * Show the form for creating a new user
     */
    public function create()
    {
        $user = auth()->user();
        
        if ($user->hasRole('super admin')) {
            // Super admin can create ministry admin users
            $ministries = Ministry::where(function($query) {
                $query->where('isActive', true)->orWhereNull('isActive');
            })->orderBy('name')->get();
            
            $roles = Role::whereIn('name', ['admin'])->get();
            $departments = collect(); // Will be loaded via AJAX
            
        } elseif ($user->hasRole('admin')) {
            // Ministry admin can create PD users for their ministry
            $userMinistry = $user->userMinistries()->first();
            if (!$userMinistry) {
                abort(403, 'No ministry assignment found');
            }
            
            $ministries = Ministry::where('id', $userMinistry->ministry_id)->get();
            $departments = Department::where('ministry_id', $userMinistry->ministry_id)
                ->where(function($query) {
                    $query->where('isActive', true)->orWhereNull('isActive');
                })
                ->orderBy('name')->get();
            
            $roles = Role::whereIn('name', ['PD'])->get();
            
        } else {
            abort(403, 'Unauthorized access');
        }

        return view('users.create', compact('ministries', 'departments', 'roles'));
    }

    /**
     * Store a newly created user in storage
     */
    public function store(Request $request)
    {
        $user = auth()->user();
        
        // Define validation rules based on user role
        $rules = [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|exists:roles,name',
        ];

        if ($user->hasRole('super admin')) {
            $rules['ministry_id'] = 'required|exists:ministries,id';
            // Department is optional for ministry admin users
        } elseif ($user->hasRole('admin')) {
            $rules['ministry_id'] = 'required|exists:ministries,id';
            $rules['department_id'] = 'required|exists:departments,id';
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::beginTransaction();

            // Create the user
            $newUser = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'email_verified_at' => now(),
            ]);

            // Assign role
            $newUser->assignRole($request->role);

            // Create user ministry assignment
            $userMinistryData = [
                'user_id' => $newUser->id,
                'ministry_id' => $request->ministry_id,
                'is_active' => true,
            ];

            // For PD users, assign specific department
            if ($request->role === 'PD') {
                $userMinistryData['department_id'] = $request->department_id;
            }

            UserMinistry::create($userMinistryData);

            DB::commit();

            return redirect()->route('users.index')
                ->with('success', 'User created successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'Error creating user: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified user
     */
    public function show(User $user)
    {
        $user->load(['roles', 'userMinistries.ministry', 'userMinistries.department']);
        return view('users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified user
     */
    public function edit(User $user)
    {
        $currentUser = auth()->user();
        
        // Check permissions
        if ($currentUser->hasRole('super admin')) {
            // Super admin can edit both ministry admin and PD users
            if (!$user->hasRole('admin') && !$user->hasRole('PD')) {
                abort(403, 'Can only edit ministry admin and PD users');
            }
        } elseif ($currentUser->hasRole('admin')) {
            // Ministry admin can edit PD users from their ministry
            if (!$user->hasRole('PD')) {
                abort(403, 'Can only edit PD users');
            }
            
            $currentUserMinistry = $currentUser->userMinistries()->first();
            $userMinistry = $user->userMinistries()->first();
            
            if (!$currentUserMinistry || !$userMinistry || 
                $currentUserMinistry->ministry_id !== $userMinistry->ministry_id) {
                abort(403, 'Can only edit users from your ministry');
            }
        } else {
            abort(403, 'Unauthorized access');
        }

        $user->load(['userMinistries.ministry', 'userMinistries.department']);
        
        if ($currentUser->hasRole('super admin')) {
            $ministries = Ministry::where(function($query) {
                $query->where('isActive', true)->orWhereNull('isActive');
            })->orderBy('name')->get();

            // If editing a PD user, load departments for their ministry
            if ($user->hasRole('PD')) {
                $userMinistry = $user->userMinistries()->first();
                if ($userMinistry) {
                    $departments = Department::where('ministry_id', $userMinistry->ministry_id)
                        ->where(function($query) {
                            $query->where('isActive', true)->orWhereNull('isActive');
                        })
                        ->orderBy('name')->get();
                } else {
                    $departments = collect();
                }
            } else {
                $departments = collect();
            }
        } else {
            $userMinistry = $currentUser->userMinistries()->first();
            $ministries = Ministry::where('id', $userMinistry->ministry_id)->get();
            $departments = Department::where('ministry_id', $userMinistry->ministry_id)
                ->where(function($query) {
                    $query->where('isActive', true)->orWhereNull('isActive');
                })
                ->orderBy('name')->get();
        }

        return view('users.edit', compact('user', 'ministries', 'departments'));
    }

    /**
     * Update the specified user in storage
     */
    public function update(Request $request, User $user)
    {
        $currentUser = auth()->user();
        
        // Define validation rules
        $rules = [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'password' => 'nullable|string|min:8|confirmed',
        ];

        if ($currentUser->hasRole('super admin')) {
            $rules['ministry_id'] = 'required|exists:ministries,id';
            // If editing a PD user, department is also required
            if ($user->hasRole('PD')) {
                $rules['department_id'] = 'required|exists:departments,id';
            }
        } elseif ($currentUser->hasRole('admin')) {
            $rules['ministry_id'] = 'required|exists:ministries,id';
            $rules['department_id'] = 'required|exists:departments,id';
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::beginTransaction();

            // Update user basic info
            $user->name = $request->name;
            $user->email = $request->email;
            
            if ($request->filled('password')) {
                $user->password = Hash::make($request->password);
            }
            
            $user->save();

            // Update user ministry assignment
            $userMinistry = $user->userMinistries()->first();
            if ($userMinistry) {
                $userMinistry->ministry_id = $request->ministry_id;
                
                if ($user->hasRole('PD')) {
                    $userMinistry->department_id = $request->department_id;
                }
                
                $userMinistry->save();
            }

            DB::commit();

            return redirect()->route('users.index')
                ->with('success', 'User updated successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'Error updating user: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified user from storage
     */
    public function destroy(User $user)
    {
        $currentUser = auth()->user();
        
        // Prevent self-deletion
        if ($user->id === $currentUser->id) {
            return redirect()->back()
                ->with('error', 'You cannot delete your own account.');
        }

        // Check permissions
        if ($currentUser->hasRole('super admin')) {
            if (!$user->hasRole('admin')) {
                return redirect()->back()
                    ->with('error', 'Can only delete ministry admin users.');
            }
        } elseif ($currentUser->hasRole('admin')) {
            if (!$user->hasRole('PD')) {
                return redirect()->back()
                    ->with('error', 'Can only delete PD users.');
            }
            
            $currentUserMinistry = $currentUser->userMinistries()->first();
            $userMinistry = $user->userMinistries()->first();
            
            if (!$currentUserMinistry || !$userMinistry || 
                $currentUserMinistry->ministry_id !== $userMinistry->ministry_id) {
                return redirect()->back()
                    ->with('error', 'Can only delete users from your ministry.');
            }
        } else {
            abort(403, 'Unauthorized access');
        }

        try {
            DB::beginTransaction();

            // Delete user ministry assignments
            $user->userMinistries()->delete();
            
            // Delete the user
            $user->delete();

            DB::commit();

            return redirect()->route('users.index')
                ->with('success', 'User deleted successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'Error deleting user: ' . $e->getMessage());
        }
    }

    /**
     * Get departments for a ministry (AJAX endpoint)
     */
    public function getDepartments($ministryId)
    {
        $departments = Department::where('ministry_id', $ministryId)
            ->where(function($query) {
                $query->where('isActive', true)->orWhereNull('isActive');
            })
            ->orderBy('name')
            ->get(['id', 'name']);

        return response()->json($departments);
    }
}
