@extends('layouts.app')

@section('title', 'PSDP Projects')

@section('content')
<div class="bg-[#ebf3f8] min-h-screen">
    <h2 class="text-[24px] text-[#0D163A] font-semibold ps-5 mt-5">PSDP Management Dashboard</h2>

    <div class="flex items-center space-x-4 p-5 mt-5">
        <!-- Search input -->
        <div class="flex items-center bg-white rounded-full px-4 py-3 w-[70%] shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
            <img src="{{ asset('assets/search-normal.svg') }}" alt="Search" class="w-5 h-5 text-gray-400">
            <input
                type="text"
                id="projectSearch"
                placeholder="Search projects by name, department, or PSDP ID..."
                class="bg-transparent focus:outline-none outline-none focus:ring-0 border-0 w-full text-sm text-gray-700 ml-3 placeholder-gray-400"
            />
            <button id="clearSearch" class="hidden ml-2 text-gray-400 hover:text-gray-600">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <!-- Sort dropdown -->
        <div class="w-[30%] relative">
            <select id="sortSelect" class="bg-white w-full text-gray-700 text-sm rounded-full px-4 py-3 border border-gray-200 shadow-sm hover:shadow-md transition-shadow focus:outline-none focus:ring-2 focus:ring-[#1A92AA] focus:border-transparent appearance-none cursor-pointer">
                <option value="name">Sort by: Project Name</option>
                <option value="department">Sort by: Department</option>
                <option value="total_cost">Sort by: Total Budget</option>
                <option value="utilization">Sort by: Utilization</option>
                <option value="created_at">Sort by: Date Created</option>
                <option value="status">Sort by: Status</option>
            </select>
            <!-- Custom dropdown arrow -->
            <div class="pointer-events-none absolute inset-y-0 right-4 flex items-center">
                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </div>
        </div>
    </div>



    <!-- Search Results Info -->
    <div id="searchInfo" class="hidden px-5 pb-2">
        <div class="bg-blue-50 border border-blue-200 rounded-lg px-4 py-2">
            <p class="text-sm text-blue-700">
                <span id="searchResultsCount">0</span> projects found
                <span id="searchTerm" class="font-semibold"></span>
                <button id="clearSearchInfo" class="ml-2 text-blue-600 hover:text-blue-800 underline">Clear search</button>
            </p>
        </div>
    </div>

    <div class="p-5 flex flex-col gap-10" id="projectsList">
        @forelse($projects as $project)
        <div class="w-full bg-white rounded-md p-5 flex gap-10 project-card"
             data-name="{{ strtolower($project->name) }}"
             data-department="{{ strtolower($project->department->name ?? '') }}"
             data-total-cost="{{ $project->total_cost ?? 0 }}"
             data-created-at="{{ $project->created_at }}"
             data-status="{{ strtolower($project->status->name ?? 'active') }}">

            <div class="w-[50%] flex flex-col justify-around gap-5">
                <div>
                    <p class="text-[#0D163A] text-[20px] font-semibold">{{ $project->name }}</p>
                    <p class="text-[#0D163A] text-[16px] mt-5">{{ $project->department->name ?? 'N/A' }}</p>
                    <p class="project-psdp-id text-[#0D163A] text-[12px] mt-1 opacity-75">PSDP ID: {{ $project->psdp_id }}</p>
                </div>

                <div class="w-full">
                    <table class="w-full border-separate border-spacing-y-[15px]">
                        <thead class="text-left">
                            <th>Total Budget</th>
                            <th>Allocated</th>
                            <th>Expenditure</th>
                            <th>Cumulative Exp</th>
                        </thead>
                        <tbody>
                            <tr>
                                @php
                                    $totalBudget = $project->total_cost ?? $project->grand_amount ?? 0;
                                    $allocated = $project->allocations->sum('allocation_amount') ?? 0;
                                    $expenditure = $project->allocations->sum('expense_amount') ?? 0;
                                    $cumulativeExp = $project->allocations->sum('release_amount') ?? 0;
                                @endphp
                                <td>Rs. {{ number_format($totalBudget / 1000000, 2) }} M</td>
                                <td>Rs. {{ number_format($allocated / 1000000, 2) }} M</td>
                                <td>Rs. {{ number_format($expenditure / 1000000, 2) }} M</td>
                                <td>Rs. {{ number_format($cumulativeExp / 1000000, 2) }} M</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="w-[50%]">
                @php
                    $utilizationPercentage = $cumulativeExp > 0 ? ($expenditure / $cumulativeExp) * 100 : 0;
                    $statusColor = $utilizationPercentage >= 70 ? '#EBF3F8' : '#F8EBED';
                    $iconColor = $utilizationPercentage >= 70 ? 'coin-icon.svg' : 'coin-icon-red.svg';
                @endphp
                <div class="w-full bg-[#EBF3F8] p-[20px] rounded-[18px] h-full">
                    <div class="flex justify-between items-center pb-5 border-b border-[#B6B6B6]">
                        <div class="flex items-center gap-3">
                            <img src="{{ asset('assets/coin-icon.svg') }}" alt="">
                            <p class="text-[18px] font-semibold">Project Status</p>
                        </div>

                        <a href="{{ route('psdp.projects.show', $project) }}" class="flex items-center gap-3 cursor-pointer" >
                            <img src="{{ asset('assets/arrow-chart2.svg') }}">
                            <p class="text-[10px] font-semibold">View Details</p>
                        </a>
                    </div>

                    <div class="flex justify-between items-center mt-5">
                        <div class="flex flex-col gap-3">
                            <p class="text-[10px] font-semibold">Utilization against amount released</p>
                            <button class="utilization-btn cursor-pointer bg-[#1A92AA] text-white px-8 py-3 rounded-full text-[18px] font-semibold">
                                {{ number_format($utilizationPercentage, 1) }}% Utilization
                            </button>
                        </div>

                        <div id="chart2-{{ $project->id }}" class="w-[112px] h-[60px]"></div>
                    </div>
                </div>
            </div>
        </div>
        @empty
        <div class="w-full bg-white rounded-md p-10 text-center">
            <p class="text-gray-500 text-lg">No projects found.</p>
            <a href="{{ route('psdp.projects.create') }}"
               class="inline-block mt-4 bg-[#1A92AA] text-white px-6 py-3 rounded-full hover:bg-[#157a94] transition-colors">
                Create First Project
            </a>
        </div>
        @endforelse
    </div>

    <div class="p-5 w-full flex justify-end items-center">
        <nav aria-label="Page navigation example">
            <ul class="flex items-center -space-x-px h-8 text-sm">
                @if($projects->onFirstPage())
                <li>
                    <span class="flex items-center justify-center px-3 h-8 ms-0 leading-tight text-gray-300 bg-white border border-e-0 border-gray-300 rounded-s-lg">
                        <span class="sr-only">Previous</span>
                        <svg class="w-2.5 h-2.5 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 1 1 5l4 4"/>
                        </svg>
                    </span>
                </li>
                @else
                <li>
                    <a href="{{ $projects->previousPageUrl() }}" class="flex items-center justify-center px-3 h-8 ms-0 leading-tight text-gray-500 bg-white border border-e-0 border-gray-300 rounded-s-lg hover:bg-gray-100 hover:text-gray-700">
                        <span class="sr-only">Previous</span>
                        <svg class="w-2.5 h-2.5 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 1 1 5l4 4"/>
                        </svg>
                    </a>
                </li>
                @endif

                @foreach ($projects->getUrlRange(1, $projects->lastPage()) as $page => $url)
                    @if ($page == $projects->currentPage())
                        <li>
                            <span aria-current="page" class="flex items-center justify-center px-3 h-8 text-blue-600 border border-gray-300 bg-blue-50 hover:bg-blue-100 hover:text-blue-700">{{ $page }}</span>
                        </li>
                    @else
                        <li>
                            <a href="{{ $url }}" class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700">{{ $page }}</a>
                        </li>
                    @endif
                @endforeach

                @if($projects->hasMorePages())
                <li>
                    <a href="{{ $projects->nextPageUrl() }}" class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 rounded-e-lg hover:bg-gray-100 hover:text-gray-700">
                        <span class="sr-only">Next</span>
                        <svg class="w-2.5 h-2.5 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                        </svg>
                    </a>
                </li>
                @else
                <li>
                    <span class="flex items-center justify-center px-3 h-8 leading-tight text-gray-300 bg-white border border-gray-300 rounded-e-lg">
                        <span class="sr-only">Next</span>
                        <svg class="w-2.5 h-2.5 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                        </svg>
                    </span>
                </li>
                @endif
            </ul>
        </nav>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize charts for each project
    @foreach($projects as $project)
        @php
            $totalBudget = $project->total_cost ?? $project->grand_amount ?? 0;
            $allocated = $project->allocations->sum('allocation_amount') ?? 0;
            $expenditure = $project->allocations->sum('expense_amount') ?? 0;
            $cumulativeExp = $project->allocations->sum('release_amount') ?? 0;
            $utilizationPercentage = $cumulativeExp > 0 ? ($expenditure / $cumulativeExp) * 100 : 0;

            // Generate trend data based on utilization (you can replace this with actual historical data)
            $trendData = [
                max(0, $utilizationPercentage - 20),
                max(0, $utilizationPercentage - 10),
                max(0, $utilizationPercentage - 5),
                $utilizationPercentage
            ];
        @endphp

        var options{{ $project->id }} = {
            series: [{
                name: 'Utilization Trend',
                data: [{{ implode(',', $trendData) }}]
            }],
            chart: {
                type: 'area',
                height: 60,
                toolbar: {
                    show: false
                },
                zoom: {
                    enabled: false
                }
            },
            stroke: {
                curve: 'smooth',
                width: 3,
                colors: ['#14B8A6']
            },
            fill: {
                type: 'gradient',
                gradient: {
                    shade: 'light',
                    type: 'vertical',
                    shadeIntensity: 0.5,
                    gradientToColors: ['transparent'],
                    inverseColors: false,
                    opacityFrom: 0.4,
                    opacityTo: 0,
                    stops: [0, 100]
                }
            },
            colors: ['#14B8A6'],
            grid: {
                show: false
            },
            xaxis: {
                labels: {
                    show: false
                },
                axisBorder: {
                    show: false
                },
                axisTicks: {
                    show: false
                },
                categories: [1, 2, 3, 4]
            },
            yaxis: {
                labels: {
                    show: false
                }
            },
            tooltip: {
                enabled: false
            },
            markers: {
                size: 0
            },
            dataLabels: {
                enabled: false
            }
        };

        var chart{{ $project->id }} = new ApexCharts(document.querySelector("#chart2-{{ $project->id }}"), options{{ $project->id }});
        chart{{ $project->id }}.render();
    @endforeach

    // Enhanced search functionality
    const searchInput = document.getElementById('projectSearch');
    const clearSearchBtn = document.getElementById('clearSearch');
    const clearSearchInfoBtn = document.getElementById('clearSearchInfo');
    const searchInfo = document.getElementById('searchInfo');
    const searchResultsCount = document.getElementById('searchResultsCount');
    const searchTermSpan = document.getElementById('searchTerm');
    const projectCards = document.querySelectorAll('.project-card');
    const totalProjects = projectCards.length;

    function performSearch() {
        const searchTerm = searchInput.value.toLowerCase().trim();
        let visibleCount = 0;

        projectCards.forEach(card => {
            const name = card.dataset.name || '';
            const department = card.dataset.department || '';
            const psdpId = card.querySelector('.project-psdp-id')?.textContent.toLowerCase() || '';

            const isVisible = searchTerm === '' ||
                            name.includes(searchTerm) ||
                            department.includes(searchTerm) ||
                            psdpId.includes(searchTerm);

            if (isVisible) {
                card.style.display = 'flex';
                visibleCount++;
            } else {
                card.style.display = 'none';
            }
        });

        // Update search info
        if (searchTerm === '') {
            searchInfo.classList.add('hidden');
            clearSearchBtn.classList.add('hidden');
        } else {
            searchInfo.classList.remove('hidden');
            clearSearchBtn.classList.remove('hidden');
            searchResultsCount.textContent = visibleCount;
            searchTermSpan.textContent = `for "${searchTerm}"`;
        }
    }

    function clearSearch() {
        searchInput.value = '';
        performSearch();
    }

    // Search event listeners
    searchInput.addEventListener('input', performSearch);
    clearSearchBtn.addEventListener('click', clearSearch);
    clearSearchInfoBtn.addEventListener('click', clearSearch);

    // Enhanced sort functionality
    const sortSelect = document.getElementById('sortSelect');

    function sortProjects() {
        const sortBy = sortSelect.value;
        const projectsList = document.getElementById('projectsList');
        const cardsArray = Array.from(projectCards);

        cardsArray.sort((a, b) => {
            let aValue, bValue;

            switch(sortBy) {
                case 'name':
                    aValue = a.dataset.name || '';
                    bValue = b.dataset.name || '';
                    return aValue.localeCompare(bValue);

                case 'department':
                    aValue = a.dataset.department || '';
                    bValue = b.dataset.department || '';
                    return aValue.localeCompare(bValue);

                case 'total_cost':
                    aValue = parseFloat(a.dataset.totalCost) || 0;
                    bValue = parseFloat(b.dataset.totalCost) || 0;
                    return bValue - aValue; // Descending order

                case 'utilization':
                    // Extract utilization percentage from the button text
                    const aUtilBtn = a.querySelector('.utilization-btn');
                    const bUtilBtn = b.querySelector('.utilization-btn');
                    aValue = aUtilBtn ? parseFloat(aUtilBtn.textContent.match(/[\d.]+/)?.[0]) || 0 : 0;
                    bValue = bUtilBtn ? parseFloat(bUtilBtn.textContent.match(/[\d.]+/)?.[0]) || 0 : 0;
                    return bValue - aValue; // Descending order

                case 'created_at':
                    // You can add data-created-at attribute to cards for this
                    aValue = a.dataset.createdAt || '';
                    bValue = b.dataset.createdAt || '';
                    return new Date(bValue) - new Date(aValue); // Newest first

                case 'status':
                    // You can add data-status attribute to cards for this
                    aValue = a.dataset.status || '';
                    bValue = b.dataset.status || '';
                    return aValue.localeCompare(bValue);

                default:
                    return 0;
            }
        });

        // Re-append sorted cards
        cardsArray.forEach(card => {
            projectsList.appendChild(card);
        });
    }

    // Sort event listener
    sortSelect.addEventListener('change', sortProjects);
});
</script>

@endsection