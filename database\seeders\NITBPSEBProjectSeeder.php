<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PSDP\Project;
use App\Models\PSDP\Ministry;
use App\Models\PSDP\Department;
use App\Models\PSDP\ApprovingAuthority;
use App\Models\PSDP\FinancialYear;
use App\Models\PSDP\Allocation;
use App\Models\PSDP\Quarter;
use App\Models\PSDP\Deliverable;
use App\Models\PSDP\Task;
use App\Models\PSDP\Status;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class NITBPSEBProjectSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Starting NITB and PSEB Projects import...');

        try {
            DB::beginTransaction();

            // Get required entities
            $ministry = Ministry::where('name', 'Ministry of Information Technology and Telecommunication')->first();
            $nitbDepartment = Department::where('name', 'National Information Technology Board (NITB)')->first();
            $psebDepartment = Department::where('name', 'Pakistan Software Export Board (PSEB)')->first();

            // Create PSEB department if it doesn't exist
            if (!$psebDepartment && $ministry) {
                $psebDepartment = Department::create([
                    'name' => 'Pakistan Software Export Board (PSEB)',
                    'ministry_id' => $ministry->id,
                    'isActive' => true
                ]);
                $this->command->info('Created PSEB department');
            }
            
            $ddwpAuthority = ApprovingAuthority::where('name', 'DDWP')->first();
            $fy2024_25 = FinancialYear::where('name', '2024-2025')->first();
            $defaultStatus = Status::where('name', 'In Progress')->first();

            // Create missing entities if they don't exist
            if (!$ddwpAuthority) {
                $ddwpAuthority = ApprovingAuthority::create(['name' => 'DDWP']);
                $this->command->info('Created DDWP authority');
            }

            if (!$fy2024_25) {
                $fy2024_25 = FinancialYear::create(['name' => '2024-2025', 'is_active' => true]);
                $this->command->info('Created 2024-2025 financial year');
            }

            if (!$defaultStatus) {
                $defaultStatus = Status::create(['name' => 'In Progress']);
                $this->command->info('Created In Progress status');
            }

            // Get PD users
            $pdNitb = User::where('email', '<EMAIL>')->first();
            $pdPseb = User::where('email', '<EMAIL>')->first();

            if (!$ministry || !$nitbDepartment || !$psebDepartment || !$pdNitb || !$pdPseb) {
                $this->command->error('Required entities not found!');
                $this->command->error('Ministry: ' . ($ministry ? 'Found' : 'Not Found'));
                $this->command->error('NITB Department: ' . ($nitbDepartment ? 'Found' : 'Not Found'));
                $this->command->error('PSEB Department: ' . ($psebDepartment ? 'Found' : 'Not Found'));
                $this->command->error('PD NITB: ' . ($pdNitb ? 'Found' : 'Not Found'));
                $this->command->error('PD PSEB: ' . ($pdPseb ? 'Found' : 'Not Found'));
                return;
            }

            // NITB Projects (S.No 1 and 10)
            $nitbProjects = [
                [
                    'name' => 'Smart office Federal Ministries & Departments PSDP through MoITT (NITB)',
                    'psdp_no' => '657',
                    'approval_date' => '2020-09-16',
                    'completion_date_pc1' => '2024-12-31',
                    'likely_completion_date' => '2026-06-30',
                    'total_cost' => 572.800, // 572.800 million (stored as millions, will be converted to actual amount)
                    'allocation_amount' => 300, // 300 million
                    'release_amount' => 132.666, // 132.666 million
                    'expense_amount' => 132.665, // 132.665 million
                ],
                [
                    'name' => 'One Patient One ID (NITB)',
                    'psdp_no' => '666',
                    'approval_date' => '2020-04-06',
                    'completion_date_pc1' => '2025-06-30',
                    'likely_completion_date' => '2026-06-30',
                    'total_cost' => 200.247, // 200.247 million (stored as millions, will be converted to actual amount)
                    'allocation_amount' => 50, // 50 million
                    'release_amount' => 25, // 25 million
                    'expense_amount' => 20, // 20 million
                ]
            ];

            // PSEB Projects (S.No 2 and 5)
            $psebProjects = [
                [
                    'name' => 'Certification of IT Professionals (PSEB)',
                    'psdp_no' => '658',
                    'approval_date' => '2019-12-18',
                    'completion_date_pc1' => '2024-06-30',
                    'likely_completion_date' => '2025-06-30',
                    'total_cost' => 901.250, // 901.250 million (stored as millions, will be converted to actual amount)
                    'allocation_amount' => 100, // 100 million
                    'release_amount' => 246.130, // 246.130 million
                    'expense_amount' => 242.650, // 242.650 million
                ],
                [
                    'name' => 'Establishment of 25 STPs in Pakistan Phase-I (PSEB)',
                    'psdp_no' => '661',
                    'approval_date' => '2021-03-31',
                    'completion_date_pc1' => '2024-11-25',
                    'likely_completion_date' => '2025-06-30',
                    'total_cost' => 473.028, // 473.028 million (stored as millions, will be converted to actual amount)
                    'allocation_amount' => 175, // 175 million
                    'release_amount' => 98.462, // 98.462 million
                    'expense_amount' => 95, // 95 million
                ]
            ];

            $projectsCreated = 0;

            // Create NITB Projects
            foreach ($nitbProjects as $projectData) {
                $project = $this->createProject(
                    $projectData,
                    $ministry,
                    $nitbDepartment,
                    $ddwpAuthority,
                    $fy2024_25,
                    $defaultStatus,
                    $pdNitb
                );
                $projectsCreated++;
                $this->command->info("Created NITB project: {$project->name}");
            }

            // Create PSEB Projects
            foreach ($psebProjects as $projectData) {
                $project = $this->createProject(
                    $projectData,
                    $ministry,
                    $psebDepartment,
                    $ddwpAuthority,
                    $fy2024_25,
                    $defaultStatus,
                    $pdPseb
                );
                $projectsCreated++;
                $this->command->info("Created PSEB project: {$project->name}");
            }

            DB::commit();
            $this->command->info("Successfully imported {$projectsCreated} NITB and PSEB projects!");

        } catch (\Exception $e) {
            DB::rollback();
            $this->command->error('Error importing projects: ' . $e->getMessage());
        }
    }

    private function createProject($data, $ministry, $department, $authority, $financialYear, $status, $pdUser)
    {
        // Create project
        $project = Project::create([
            'name' => $data['name'],
            'ministry_id' => $ministry->id,
            'department_id' => $department->id,
            'psdp_no' => $data['psdp_no'],
            'psdp_id' => 'PSDP-' . $data['psdp_no'] . '-' . date('Y'),
            'approving_authority_id' => $authority->id,
            'authority_approved_date' => $data['approval_date'],
            'admin_approval_date' => $data['approval_date'],
            'completion_date_pc1' => $data['completion_date_pc1'],
            'likely_completion_date' => $data['likely_completion_date'],
            // Convert from millions to actual amounts
            'total_cost' => $data['total_cost'] * 1000000,
            'lc_amount' => $data['total_cost'] * 1000000, // Assuming all local currency
            'fc_amount' => 0,
            'grand_amount' => $data['total_cost'] * 1000000,
            'last_revised_date' => $data['completion_date_pc1'],
            'added_by_user_id' => $pdUser->id,
        ]);

        // Create allocation for FY 2024-25
        if ($data['allocation_amount'] > 0) {
            $allocation = Allocation::create([
                'project_id' => $project->id,
                'financial_year_id' => $financialYear->id,
                // Convert from millions to actual amounts
                'allocation_amount' => $data['allocation_amount'] * 1000000,
                'release_amount' => $data['release_amount'] * 1000000,
                'expense_amount' => $data['expense_amount'] * 1000000,
            ]);

            // Create quarterly breakdown (distribute equally across quarters)
            // Convert from millions to actual amounts for quarterly breakdown
            $quarterlyRelease = ($data['release_amount'] * 1000000) / 4;
            $quarterlyExpense = ($data['expense_amount'] * 1000000) / 4;

            for ($q = 1; $q <= 4; $q++) {
                Quarter::create([
                    'project_id' => $project->id,
                    'allocation_id' => $allocation->id,
                    'quarter' => 'Q' . $q,
                    'release_amount' => $quarterlyRelease,
                    'expense_amount' => $quarterlyExpense,
                ]);
            }
        }

        // Create default deliverable and task
        $deliverable = Deliverable::create([
            'project_id' => $project->id,
            'name' => 'Project Implementation',
        ]);

        Task::create([
            'project_id' => $project->id,
            'deliverable_id' => $deliverable->id,
            'code' => '1.1',
            'description' => 'Complete project implementation as per PC-1',
            'start_date' => $data['approval_date'],
            'end_date' => $data['completion_date_pc1'],
            'planned_complete' => 100,
            'actual_complete' => $this->calculateProgress($data),
            'status_id' => $status->id,
            'remarks' => 'Auto-generated from NITB/PSEB data',
        ]);

        return $project;
    }

    private function calculateProgress($data)
    {
        // Calculate progress based on expense vs allocation
        if ($data['allocation_amount'] > 0) {
            $progress = ($data['expense_amount'] / $data['allocation_amount']) * 100;
            return min(100, max(0, round($progress)));
        }
        return 0;
    }
}
