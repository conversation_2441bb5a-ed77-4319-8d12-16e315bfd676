<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PSDP\FinancialYear;

class FinancialYearSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $financialYears = [
            ['id' => 1, 'name' => '2010-2011'],
            ['id' => 2, 'name' => '2011-2012'],
            ['id' => 3, 'name' => '2012-2013'],
            ['id' => 4, 'name' => '2013-2014'],
            ['id' => 5, 'name' => '2014-2015'],
            ['id' => 6, 'name' => '2015-2016'],
            ['id' => 7, 'name' => '2016-2017'],
            ['id' => 8, 'name' => '2017-2018'],
            ['id' => 9, 'name' => '2018-2019'],
            ['id' => 10, 'name' => '2019-2020'],
            ['id' => 11, 'name' => '2020-2021'],
            ['id' => 12, 'name' => '2021-2022'],
            ['id' => 13, 'name' => '2022-2023'],
            ['id' => 14, 'name' => '2023-2024'],
            ['id' => 15, 'name' => '2024-2025'],
            ['id' => 16, 'name' => '2025-2026'],
        ];

        foreach ($financialYears as $year) {
            FinancialYear::updateOrCreate(
                ['id' => $year['id']],
                $year
            );
        }
    }
}
