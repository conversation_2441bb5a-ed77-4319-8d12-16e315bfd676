<?php $__env->startSection('title', 'PSDP Projects'); ?>

<?php $__env->startSection('content'); ?>
<div class="py-10 px-6">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">PSDP Projects</h1>
                <p class="text-gray-600">Manage and monitor all PSDP projects</p>
            </div>
            <div class="flex gap-3">
                <a href="<?php echo e(route('psdp.pd-projects.revisions.index')); ?>"
                   class="bg-gray-500 text-white px-6 py-3 rounded-full font-medium hover:brightness-110">
                    View All Revisions
                </a>
                <a href="<?php echo e(route('psdp.pd-projects.create')); ?>"
                   class="bg-[#1A92AA] text-white px-6 py-3 rounded-full font-medium hover:brightness-110">
                    + Create New Project
                </a>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="bg-white rounded-xl shadow p-6 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="relative">
                    <input type="text" placeholder="Search projects..." 
                           class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-[#1A92AA]">
                    <img src="<?php echo e(asset('assets/search-normal.svg')); ?>" alt="" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4">
                </div>
                
                <select class="px-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-[#1A92AA]">
                    <option>All Departments</option>
                    <option>Ignite</option>
                    <option>NITB</option>
                    <option>NTC</option>
                    <option>SCO</option>
                    <option>USF</option>
                </select>
                
                <select class="px-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-[#1A92AA]">
                    <option>All Status</option>
                    <option>Completed</option>
                    <option>Delayed</option>
                    <option>Not Started</option>
                    <option>Behind</option>
                    <option>On-track</option>
                </select>
                
                <button class="bg-[#1A92AA] text-white px-6 py-2 rounded-full font-medium hover:brightness-110">
                    <img src="<?php echo e(asset('assets/export.svg')); ?>" alt="" class="inline w-4 h-4 mr-2">
                    Export
                </button>
            </div>
        </div>

        <!-- Projects Table -->
        <div class="bg-white rounded-xl shadow overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Project Name
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                PSDP ID
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Department
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Approving Authority
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php $__empty_1 = true; $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?php echo e($project->name); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900"><?php echo e($project->psdp_id); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900"><?php echo e($project->department->name); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900"><?php echo e($project->approvingAuthority->name); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                    Active
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-3">
                                    <a href="<?php echo e(route('psdp.pd-projects.show', $project->id)); ?>"
                                       class="text-[#1A92AA] hover:text-blue-900 p-1 rounded-full hover:bg-blue-50"
                                       title="View Project">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                        </svg>
                                    </a>
                                    <a href="<?php echo e(route('psdp.pd-projects.revisions.create', $project->id)); ?>"
                                       class="text-green-600 hover:text-green-900 p-1 rounded-full hover:bg-green-50"
                                       title="Add Revision">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                        </svg>
                                    </a>
                                    <a href="<?php echo e(route('psdp.pd-projects.edit', $project->id)); ?>"
                                       class="text-indigo-600 hover:text-indigo-900 p-1 rounded-full hover:bg-indigo-50"
                                       title="Edit Project">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                        </svg>
                                    </a>
                                    <form method="POST" action="<?php echo e(route('psdp.pd-projects.destroy', $project->id)); ?>"
                                          class="inline" onsubmit="return confirm('Are you sure?')">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="text-red-600 hover:text-red-900 p-1 rounded-full hover:bg-red-50"
                                                title="Delete Project">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                            </svg>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <img src="<?php echo e(asset('assets/empty-wallet.png')); ?>" alt="" class="mx-auto w-16 h-16 mb-4 opacity-50">
                                    <p class="text-lg font-medium">No projects found</p>
                                    <p class="text-sm">Get started by creating your first PSDP project.</p>
                                    <a href="<?php echo e(route('psdp.pd-projects.create')); ?>"
                                       class="inline-block mt-4 bg-[#1A92AA] text-white px-6 py-2 rounded-full font-medium hover:brightness-110">
                                        Create Project
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            
            <?php if($projects->hasPages()): ?>
            <div class="px-6 py-4 border-t border-gray-200">
                <?php echo e($projects->links()); ?>

            </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\psdb-dashboard-backend\resources\views/psdp/pd_projects/index.blade.php ENDPATH**/ ?>