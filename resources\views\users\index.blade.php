@extends('layouts.app')

@section('title', 'User Management')

@section('content')
<div class="py-10 px-6">
    <div class="max-w-full mx-auto bg-white rounded-xl shadow p-5 sm:p-12 sm:px-16">
        <!-- Header -->
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
            <div>
                <h2 class="text-xl sm:text-2xl font-semibold text-gray-900 mb-2">
                    User Management
                </h2>
                <p class="text-gray-600">
                    @if(auth()->user()->hasRole('super admin'))
                        Manage ministry admin users
                    @elseif(auth()->user()->hasRole('admin'))
                        Manage PD users in your ministry
                    @endif
                </p>
            </div>
            <a href="{{ route('users.create') }}" 
               class="inline-flex items-center px-4 py-2 text-sm font-medium text-white rounded-full hover:brightness-110"
               style="background-color: #1A92AA;">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                </svg>
                @if(auth()->user()->hasRole('super admin'))
                    Add Ministry User
                @else
                    Add PD User
                @endif
            </a>
        </div>

        <!-- Success/Error Messages -->
        @if(session('success'))
            <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                {{ session('success') }}
            </div>
        @endif

        @if(session('error'))
            <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                {{ session('error') }}
            </div>
        @endif

        <!-- Users Table -->
        <div class="overflow-x-auto">
            <table class="w-full border-collapse border border-gray-300 rounded-lg">
                <thead>
                    <tr class="bg-gray-50">
                        <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Name</th>
                        <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Email</th>
                        <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Role</th>
                        <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Ministry</th>
                        <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Department</th>
                        <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Created</th>
                        <th class="border border-gray-300 px-4 py-3 text-center text-sm font-semibold text-gray-700">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($users as $user)
                    <tr class="hover:bg-gray-50">
                        <td class="border border-gray-300 px-4 py-3 text-sm text-gray-900">
                            {{ $user->name }}
                        </td>
                        <td class="border border-gray-300 px-4 py-3 text-sm text-gray-900">
                            {{ $user->email }}
                        </td>
                        <td class="border border-gray-300 px-4 py-3 text-sm">
                            @foreach($user->roles as $role)
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    @if($role->name === 'super admin') bg-purple-100 text-purple-800
                                    @elseif($role->name === 'admin') bg-blue-100 text-blue-800
                                    @elseif($role->name === 'PD') bg-green-100 text-green-800
                                    @else bg-gray-100 text-gray-800 @endif">
                                    {{ ucfirst($role->name) }}
                                </span>
                            @endforeach
                        </td>
                        <td class="border border-gray-300 px-4 py-3 text-sm text-gray-900">
                            @if($user->userMinistries->first())
                                {{ $user->userMinistries->first()->ministry->name ?? 'N/A' }}
                            @else
                                N/A
                            @endif
                        </td>
                        <td class="border border-gray-300 px-4 py-3 text-sm text-gray-900">
                            @if($user->userMinistries->first() && $user->userMinistries->first()->department)
                                {{ $user->userMinistries->first()->department->name }}
                            @else
                                All Departments
                            @endif
                        </td>
                        <td class="border border-gray-300 px-4 py-3 text-sm text-gray-900">
                            {{ $user->created_at->format('M d, Y') }}
                        </td>
                        <td class="border border-gray-300 px-4 py-3 text-center">
                            <div class="flex justify-center space-x-3">
                                <a href="{{ route('users.show', $user) }}"
                                   class="text-blue-600 hover:text-blue-800 p-1 rounded-full hover:bg-blue-50"
                                   title="View User">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                    </svg>
                                </a>
                                <a href="{{ route('users.edit', $user) }}"
                                   class="text-green-600 hover:text-green-800 p-1 rounded-full hover:bg-green-50"
                                   title="Edit User">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                    </svg>
                                </a>
                                @if($user->id !== auth()->id())
                                <form action="{{ route('users.destroy', $user) }}" method="POST" class="inline"
                                      onsubmit="return confirm('Are you sure you want to delete this user?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="text-red-600 hover:text-red-800 p-1 rounded-full hover:bg-red-50"
                                            title="Delete User">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                        </svg>
                                    </button>
                                </form>
                                @endif
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="7" class="border border-gray-300 px-4 py-8 text-center text-gray-500">
                            No users found.
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        @if($users->hasPages())
            <div class="mt-6">
                {{ $users->links() }}
            </div>
        @endif
    </div>
</div>
@endsection
