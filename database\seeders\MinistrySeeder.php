<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PSDP\Ministry;

class MinistrySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $ministries = [
            ['id' => 1, 'name' => 'Cabinet Secretariat', 'isActive' => null],
            ['id' => 2, 'name' => 'Ministry of Climate Change', 'isActive' => null],
            ['id' => 3, 'name' => 'Ministry of Commerce', 'isActive' => null],
            ['id' => 4, 'name' => 'Ministry of Communications', 'isActive' => null],
            ['id' => 5, 'name' => 'Ministry of Defence', 'isActive' => null],
            ['id' => 6, 'name' => 'Ministry of Defence Production', 'isActive' => null],
            ['id' => 7, 'name' => 'Ministry of Economic Affairs', 'isActive' => null],
            ['id' => 8, 'name' => 'Ministry of Energy', 'isActive' => null],
            ['id' => 9, 'name' => 'Ministry of Federal Education, Professional Training, National Heritage & Culture', 'isActive' => null],
            ['id' => 10, 'name' => 'Ministry of Finance & Revenue', 'isActive' => null],
            ['id' => 11, 'name' => 'Ministry of Foreign Affairs', 'isActive' => null],
            ['id' => 12, 'name' => 'Ministry of Housing and Works', 'isActive' => null],
            ['id' => 13, 'name' => 'Ministry of Human Rights', 'isActive' => null],
            ['id' => 14, 'name' => 'Ministry of Industries and Production', 'isActive' => null],
            ['id' => 15, 'name' => 'Ministry of Information and Broadcasting', 'isActive' => null],
            ['id' => 16, 'name' => 'Ministry of Information Technology and Telecommunication', 'isActive' => null],
            ['id' => 17, 'name' => 'Ministry of Interior', 'isActive' => null],
            ['id' => 18, 'name' => 'Ministry of Inter-Provincial Coordination', 'isActive' => null],
            ['id' => 19, 'name' => 'Ministry of Kashmir Affairs and Gilgit Baltistan', 'isActive' => null],
            ['id' => 20, 'name' => 'Ministry of Law and Justice', 'isActive' => null],
            ['id' => 21, 'name' => 'Ministry of Maritime Affairs', 'isActive' => null],
            ['id' => 22, 'name' => 'Ministry of Narcotics Control', 'isActive' => null],
            ['id' => 23, 'name' => 'Ministry of National Food Security and Research', 'isActive' => null],
            ['id' => 24, 'name' => 'Ministry of National Health Services, Regulations and Coordination', 'isActive' => null],
            ['id' => 25, 'name' => 'Ministry of Overseas Pakistanis and Human Resource Development', 'isActive' => null],
            ['id' => 26, 'name' => 'Ministry of Parliamentary Affairs', 'isActive' => null],
            ['id' => 27, 'name' => 'Ministry of Planning, Development & Special Initiatives', 'isActive' => null],
            ['id' => 28, 'name' => 'Ministry of Privatization', 'isActive' => null],
            ['id' => 29, 'name' => 'Ministry of Railways', 'isActive' => null],
            ['id' => 30, 'name' => 'Ministry of Religious Affairs and Inter Faith Harmony', 'isActive' => null],
            ['id' => 31, 'name' => 'Ministry of Science and Technology', 'isActive' => null],
            ['id' => 32, 'name' => 'Ministry of States and Frontier Regions', 'isActive' => null],
            ['id' => 33, 'name' => 'Ministry of Water Resources', 'isActive' => null],
            ['id' => 34, 'name' => 'Prime Minister Office', 'isActive' => null],
            ['id' => 35, 'name' => 'President Secretariat', 'isActive' => null],
        ];

        foreach ($ministries as $ministry) {
            Ministry::updateOrCreate(
                ['id' => $ministry['id']],
                $ministry
            );
        }
    }
}
