<?php

namespace App\Models\PSDP;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Ministry extends Model
{
    protected $table = 'minestries';

    protected $fillable = [
        'name',
        'isActive'
    ];

    protected $casts = [
        'isActive' => 'boolean'
    ];

    /**
     * Get the divisions for this ministry.
     */
    public function divisions(): HasMany
    {
        return $this->hasMany(Division::class, 'ministry_id');
    }

    /**
     * Get the departments for this ministry.
     */
    public function departments(): HasMany
    {
        return $this->hasMany(Department::class, 'ministry_id');
    }

    /**
     * Get the user ministries for this ministry.
     */
    public function userMinistries(): HasMany
    {
        return $this->hasMany(UserMinistry::class);
    }

    /**
     * Get the users assigned to this ministry.
     */
    public function users()
    {
        return $this->belongsToMany(\App\Models\User::class, 'user_ministries')
                    ->withPivot('department_id', 'is_active')
                    ->withTimestamps();
    }
}
