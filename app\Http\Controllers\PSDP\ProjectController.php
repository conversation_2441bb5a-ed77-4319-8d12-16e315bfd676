<?php

namespace App\Http\Controllers\PSDP;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\PSDP\Project;
use App\Models\PSDP\Department;
use App\Models\PSDP\ApprovingAuthority;
use App\Models\PSDP\Status;
use App\Models\PSDP\FinancialYear;
use App\Models\PSDP\Ministry;
use App\Models\PSDP\Division;
use App\Models\PSDP\UserMinistry;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class ProjectController extends Controller
{
    public function __construct()
    {
        // Middleware will be handled in routes or via middleware groups
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = auth()->user();
        $projectsQuery = Project::with(['department', 'approvingAuthority', 'allocations']);

        // Apply role-based filtering
        if ($user->hasRole('super admin')) {
            // Super admin sees all projects
        } elseif ($user->hasRole('admin')) {
            // Ministry admin sees projects from their ministry departments
            $userMinistries = UserMinistry::where('user_id', $user->id)->pluck('ministry_id');
            $projectsQuery->whereHas('department', function($query) use ($userMinistries) {
                $query->whereIn('ministry_id', $userMinistries);
            });
        } elseif ($user->hasRole('PD')) {
            // PD sees only projects they added
            $projectsQuery->where('added_by_user_id', $user->id);
        }

        $projects = $projectsQuery->paginate(10);

        return view('psdp.projects.index', compact('projects'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $user = auth()->user();
        $userMinistry = $user->userMinistries()->with(['ministry', 'department'])->first();

        // Get ministries, departments based on user role
        $ministries = collect();
        $departments = collect();

        if ($user->hasRole('super admin')) {
            // Super admin can see all ministries and departments
            $ministries = Ministry::where(function($query) {
                $query->where('isActive', true)->orWhereNull('isActive');
            })->get();

            $departments = Department::where(function($query) {
                $query->where('isActive', true)->orWhereNull('isActive');
            })->get();
        } elseif ($user->hasRole('admin')) {
            // Ministry user can only see departments in their ministry
            if ($userMinistry) {
                $departments = Department::where('ministry_id', $userMinistry->ministry_id)
                    ->where(function($query) {
                        $query->where('isActive', true)->orWhereNull('isActive');
                    })->get();
            }
        }
        // PD users don't need to select ministry or department (will be auto-filled)

        $approvingAuthorities = ApprovingAuthority::all();
        $statuses = Status::all();
        $financialYears = FinancialYear::where('is_active', true)->orWhereNull('is_active')->get();

        return view('psdp.projects.create', compact(
            'ministries',
            'departments',
            'approvingAuthorities',
            'statuses',
            'financialYears',
            'userMinistry'
        ));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $user = auth()->user();
        $userMinistry = $user->userMinistries()->first();

        // Determine department_id based on user role
        $departmentId = null;
        if ($user->hasRole('super admin')) {
            // Super admin must select department
            $departmentId = $request->department_id;
        } elseif ($user->hasRole('admin')) {
            // Ministry user must select department from their ministry
            $departmentId = $request->department_id;
        } elseif ($user->hasRole('PD')) {
            // PD user's department is auto-filled from their assignment
            $departmentId = $userMinistry ? $userMinistry->department_id : null;
        }





        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'department_id' => $user->hasRole('PD') ? 'nullable' : 'required|exists:departments,id',
            'psdp_no' => 'required|string|max:255',
            'psdp_id' => 'required|string|max:255',
            'approving_authority_id' => 'required|exists:approving_authorities,id',
            'authority_approved_date' => 'nullable|date',
            'admin_approval_date' => 'nullable|date',
            'completion_date_pc1' => [
                'nullable',
                'date',
                function ($attribute, $value, $fail) use ($request) {
                    if ($value && $request->admin_approval_date) {
                        if (strtotime($value) <= strtotime($request->admin_approval_date)) {
                            $fail('Completion Date PC1 must be greater than Admin Approval Date.');
                        }
                    }
                },
            ],
            'likely_completion_date' => [
                'nullable',
                'date',
                function ($attribute, $value, $fail) use ($request) {
                    if ($value && $request->completion_date_pc1) {
                        if (strtotime($value) < strtotime($request->completion_date_pc1)) {
                            $fail('Likely Completion Date must be greater than or equal to Completion Date PC1.');
                        }
                    }
                },
            ],
            'total_cost' => 'nullable|numeric|min:0',
            'lc_amount' => 'nullable|numeric|min:0',
            'fc_amount' => 'nullable|numeric|min:0',
            'allocations' => 'required|array|min:1',
            'allocations.*.financial_year_id' => 'required|exists:financial_years,id',
            'allocations.*.expense_amount' => 'nullable|numeric|min:0',
            'allocations.*.allocation_amount' => 'required|numeric|min:0',
            'allocations.*.release_amount' => 'nullable|numeric|min:0',
            'allocations.*.surrender_amount' => 'nullable|numeric|min:0',
            'allocations.*.quarters' => 'nullable|array',
            'allocations.*.quarters.*.release_amount' => 'nullable|numeric|min:0',
            'allocations.*.quarters.*.expense_amount' => 'nullable|numeric|min:0',
            'deliverables' => 'required|array|min:1',
            'deliverables.*.name' => 'required|string|max:255',
            'deliverables.*.tasks' => 'required|array|min:1',
            'deliverables.*.tasks.*.code' => 'required|string|max:50',
            'deliverables.*.tasks.*.description' => 'required|string',
            'deliverables.*.tasks.*.start_date' => 'required|date',
            'deliverables.*.tasks.*.end_date' => 'required|date|after:deliverables.*.tasks.*.start_date',
            'deliverables.*.tasks.*.planned_complete' => 'required|numeric|min:0|max:100',
            'deliverables.*.tasks.*.actual_complete' => 'required|numeric|min:0|max:100',
            'deliverables.*.tasks.*.status_id' => 'required|exists:statuses,id',
            'deliverables.*.tasks.*.remarks' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();



            $project = Project::create([
                'name' => $request->name,
                'department_id' => $departmentId,
                'ministry_id' => $userMinistry ? $userMinistry->ministry_id : null,
                'psdp_no' => $request->psdp_no,
                'psdp_id' => $request->psdp_id,
                'approving_authority_id' => $request->approving_authority_id,
                'authority_approved_date' => $request->authority_approved_date,
                'admin_approval_date' => $request->admin_approval_date,
                'completion_date_pc1' => $request->completion_date_pc1,
                'likely_completion_date' => $request->likely_completion_date,
                'added_by_user_id' => auth()->id(),
                'is_active' => true,
                'total_cost' => $request->total_cost,
                'lc_amount' => $request->lc_amount,
                'fc_amount' => $request->fc_amount,
                'grand_amount' => $request->total_cost, // Auto-fill grand_amount with total_cost
                'last_revised_date' => $request->completion_date_pc1, // Auto-fill with completion_date_pc1
            ]);

            // Create allocations if provided
            if ($request->has('allocations')) {
                foreach ($request->allocations as $allocationData) {
                    if (!empty($allocationData['financial_year_id'])) {
                        $allocation = $project->allocations()->create([
                            'financial_year_id' => $allocationData['financial_year_id'],
                            'expense_amount' => $allocationData['expense_amount'] ?? 0,
                            'allocation_amount' => $allocationData['allocation_amount'] ?? 0,
                            'release_amount' => $allocationData['release_amount'] ?? 0,
                            'surrender_amount' => $allocationData['surrender_amount'] ?? 0,
                        ]);

                        // Create quarterly data if provided
                        if (isset($allocationData['quarters']) && is_array($allocationData['quarters'])) {
                            foreach ($allocationData['quarters'] as $quarterIndex => $quarterData) {
                                if (!empty($quarterData['release_amount']) || !empty($quarterData['expense_amount'])) {
                                    $allocation->quarters()->create([
                                        'project_id' => $project->id,
                                        'quarter' => 'Q' . ($quarterIndex + 1),
                                        'release_amount' => $quarterData['release_amount'] ?? 0,
                                        'expense_amount' => $quarterData['expense_amount'] ?? 0,
                                    ]);
                                }
                            }
                        }
                    }
                }
            }

            foreach ($request->deliverables as $deliverableData) {
                $deliverable = $project->deliverables()->create([
                    'name' => $deliverableData['name'],
                ]);

                foreach ($deliverableData['tasks'] as $taskData) {
                    $deliverable->tasks()->create([
                        'project_id' => $project->id,
                        'code' => $taskData['code'],
                        'description' => $taskData['description'],
                        'start_date' => $taskData['start_date'],
                        'end_date' => $taskData['end_date'],
                        'planned_complete' => $taskData['planned_complete'],
                        'actual_complete' => $taskData['actual_complete'],
                        'status_id' => $taskData['status_id'],
                        'remarks' => $taskData['remarks'],
                    ]);
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Project created successfully!',
                'project_id' => $project->id
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Error creating project: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $project = Project::with([
            'department',
            'approvingAuthority',
            'deliverables.tasks.status',
            'revisions.approvingAuthority',
            'revisions.addedByUser',
            'allocations.financialYear'
        ])->findOrFail($id);

        return view('psdp.projects.show', compact('project'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $project = Project::with(['deliverables.tasks', 'allocations.quarters'])->findOrFail($id);
        $user = auth()->user();
        $userMinistry = $user->userMinistries()->with(['ministry', 'department'])->first();

        // Get ministries, departments based on user role
        $ministries = collect();
        $departments = collect();

        if ($user->hasRole('super admin')) {
            // Super admin can see all ministries and departments
            $ministries = Ministry::where(function($query) {
                $query->where('isActive', true)->orWhereNull('isActive');
            })->get();

            $departments = Department::where(function($query) {
                $query->where('isActive', true)->orWhereNull('isActive');
            })->get();
        } elseif ($user->hasRole('admin')) {
            // Ministry user can only see departments in their ministry
            if ($userMinistry) {
                $departments = Department::where('ministry_id', $userMinistry->ministry_id)
                    ->where(function($query) {
                        $query->where('isActive', true)->orWhereNull('isActive');
                    })->get();
            }
        }
        // PD users don't need to select ministry or department (will be auto-filled)

        $approvingAuthorities = ApprovingAuthority::all();
        $statuses = Status::all();
        $financialYears = FinancialYear::where('is_active', true)->orWhereNull('is_active')->get();

        return view('psdp.projects.edit', compact(
            'project',
            'ministries',
            'departments',
            'approvingAuthorities',
            'statuses',
            'financialYears',
            'userMinistry'
        ));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'department_id' => 'required|exists:departments,id',
            'psdp_id' => 'required|string|max:255',
            'approving_authority_id' => 'required|exists:approving_authorities,id',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $project = Project::findOrFail($id);

        $project->update([
            'name' => $request->name,
            'department_id' => $request->department_id,
            'psdp_id' => $request->psdp_id,
            'approving_authority_id' => $request->approving_authority_id,
            'ministry_id' => $request->ministry_id,
            'authority_approved_date' => $request->authority_approved_date,
            'admin_approval_date' => $request->admin_approval_date,
            'completion_date_pc1' => $request->completion_date_pc1,
            'likely_completion_date' => $request->likely_completion_date,
            'total_cost' => $request->total_cost,
            'lc_amount' => $request->lc_amount,
            'fc_amount' => $request->fc_amount,
            'grand_amount' => $request->total_cost, // Auto-fill grand_amount with total_cost
            'last_revised_date' => $request->completion_date_pc1, // Auto-fill with completion_date_pc1
        ]);

        return redirect()->route('psdp.projects.show', $project->id)
            ->with('success', 'Project updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $project = Project::findOrFail($id);
        $project->delete();

        return redirect()->route('psdp.projects.index')
            ->with('success', 'Project deleted successfully!');
    }

    /**
     * Get divisions for a ministry (API endpoint)
     */
    public function getDivisions($ministryId)
    {
        $divisions = Division::where('ministry_id', $ministryId)
            ->where(function($query) {
                $query->where('isActive', true)->orWhereNull('isActive');
            })
            ->select('id', 'name')
            ->get();

        return response()->json($divisions);
    }

    /**
     * Get departments for a ministry (API endpoint)
     */
    public function getDepartments($ministryId)
    {
        $departments = Department::where('ministry_id', $ministryId)
            ->where(function($query) {
                $query->where('isActive', true)->orWhereNull('isActive');
            })
            ->select('id', 'name')
            ->get();

        return response()->json($departments);
    }

    /**
     * Get departments for a division (API endpoint)
     */
    public function getDepartmentsByDivision($divisionId)
    {
        $departments = Department::where('division_id', $divisionId)
            ->where(function($query) {
                $query->where('isActive', true)->orWhereNull('isActive');
            })
            ->select('id', 'name')
            ->get();

        return response()->json($departments);
    }
}
