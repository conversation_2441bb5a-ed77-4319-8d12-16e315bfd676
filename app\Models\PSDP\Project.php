<?php

namespace App\Models\PSDP;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Project extends Model
{
    protected $fillable = [
        'name',
        'ministry_id',
        'department_id',
        'psdp_no',
        'psdp_id',
        'approving_authority_id',
        'authority_approved_date',
        'admin_approval_date',
        'completion_date_pc1',
        'likely_completion_date',
        'is_revised',
        'no_of_revision',
        'last_revised_date',
        'added_by_user_id',
        'is_active',
        'updated_at_date',
        'deleted_at_date',
        'total_cost',
        'lc_amount',
        'fc_amount',
        'grand_amount'
    ];

    protected $casts = [
        'authority_approved_date' => 'date',
        'admin_approval_date' => 'date',
        'completion_date_pc1' => 'date',
        'likely_completion_date' => 'date',
        'last_revised_date' => 'date',
        'updated_at_date' => 'date',
        'deleted_at_date' => 'date',
        'is_revised' => 'boolean',
        'is_active' => 'boolean',
        'total_cost' => 'decimal:2',
        'lc_amount' => 'decimal:2',
        'fc_amount' => 'decimal:2',
        'grand_amount' => 'decimal:2',
    ];

    /**
     * Get the department that owns the project.
     */
    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * Get the approving authority that owns the project.
     */
    public function approvingAuthority(): BelongsTo
    {
        return $this->belongsTo(ApprovingAuthority::class);
    }

    /**
     * Get the allocations for the project.
     */
    public function allocations(): HasMany
    {
        return $this->hasMany(Allocation::class);
    }

    /**
     * Get the quarters for the project.
     */
    public function quarters(): HasMany
    {
        return $this->hasMany(Quarter::class);
    }

    /**
     * Get the deliverables for the project.
     */
    public function deliverables(): HasMany
    {
        return $this->hasMany(Deliverable::class);
    }

    /**
     * Get the tasks for the project.
     */
    public function tasks(): HasMany
    {
        return $this->hasMany(Task::class);
    }

    /**
     * Get the user who added this project.
     */
    public function addedByUser(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'added_by_user_id');
    }

    /**
     * Get the revisions for the project.
     */
    public function revisions(): HasMany
    {
        return $this->hasMany(Revision::class);
    }
}
