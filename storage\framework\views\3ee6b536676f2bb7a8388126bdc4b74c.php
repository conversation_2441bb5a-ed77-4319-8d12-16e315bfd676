<?php $__env->startSection('title', 'User Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="py-10 px-6">
    <div class="max-w-full mx-auto bg-white rounded-xl shadow p-5 sm:p-12 sm:px-16">
        <!-- Header -->
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
            <div>
                <h2 class="text-xl sm:text-2xl font-semibold text-gray-900 mb-2">
                    User Management
                </h2>
                <p class="text-gray-600">
                    <?php if(auth()->user()->hasRole('super admin')): ?>
                        Manage ministry admin users
                    <?php elseif(auth()->user()->hasRole('admin')): ?>
                        Manage PD users in your ministry
                    <?php endif; ?>
                </p>
            </div>
            <a href="<?php echo e(route('users.create')); ?>" 
               class="inline-flex items-center px-4 py-2 text-sm font-medium text-white rounded-full hover:brightness-110"
               style="background-color: #1A92AA;">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                </svg>
                <?php if(auth()->user()->hasRole('super admin')): ?>
                    Add Ministry User
                <?php else: ?>
                    Add PD User
                <?php endif; ?>
            </a>
        </div>

        <!-- Success/Error Messages -->
        <?php if(session('success')): ?>
            <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                <?php echo e(session('success')); ?>

            </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                <?php echo e(session('error')); ?>

            </div>
        <?php endif; ?>

        <!-- Users Table -->
        <div class="overflow-x-auto">
            <table class="w-full border-collapse border border-gray-300 rounded-lg">
                <thead>
                    <tr class="bg-gray-50">
                        <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Name</th>
                        <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Email</th>
                        <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Role</th>
                        <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Ministry</th>
                        <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Department</th>
                        <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Created</th>
                        <th class="border border-gray-300 px-4 py-3 text-center text-sm font-semibold text-gray-700">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr class="hover:bg-gray-50">
                        <td class="border border-gray-300 px-4 py-3 text-sm text-gray-900">
                            <?php echo e($user->name); ?>

                        </td>
                        <td class="border border-gray-300 px-4 py-3 text-sm text-gray-900">
                            <?php echo e($user->email); ?>

                        </td>
                        <td class="border border-gray-300 px-4 py-3 text-sm">
                            <?php $__currentLoopData = $user->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    <?php if($role->name === 'super admin'): ?> bg-purple-100 text-purple-800
                                    <?php elseif($role->name === 'admin'): ?> bg-blue-100 text-blue-800
                                    <?php elseif($role->name === 'PD'): ?> bg-green-100 text-green-800
                                    <?php else: ?> bg-gray-100 text-gray-800 <?php endif; ?>">
                                    <?php echo e(ucfirst($role->name)); ?>

                                </span>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </td>
                        <td class="border border-gray-300 px-4 py-3 text-sm text-gray-900">
                            <?php if($user->userMinistries->first()): ?>
                                <?php echo e($user->userMinistries->first()->ministry->name ?? 'N/A'); ?>

                            <?php else: ?>
                                N/A
                            <?php endif; ?>
                        </td>
                        <td class="border border-gray-300 px-4 py-3 text-sm text-gray-900">
                            <?php if($user->userMinistries->first() && $user->userMinistries->first()->department): ?>
                                <?php echo e($user->userMinistries->first()->department->name); ?>

                            <?php else: ?>
                                All Departments
                            <?php endif; ?>
                        </td>
                        <td class="border border-gray-300 px-4 py-3 text-sm text-gray-900">
                            <?php echo e($user->created_at->format('M d, Y')); ?>

                        </td>
                        <td class="border border-gray-300 px-4 py-3 text-center">
                            <div class="flex justify-center space-x-3">
                                <a href="<?php echo e(route('users.show', $user)); ?>"
                                   class="text-blue-600 hover:text-blue-800 p-1 rounded-full hover:bg-blue-50"
                                   title="View User">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                    </svg>
                                </a>
                                <a href="<?php echo e(route('users.edit', $user)); ?>"
                                   class="text-green-600 hover:text-green-800 p-1 rounded-full hover:bg-green-50"
                                   title="Edit User">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                    </svg>
                                </a>
                                <?php if($user->id !== auth()->id()): ?>
                                <form action="<?php echo e(route('users.destroy', $user)); ?>" method="POST" class="inline"
                                      onsubmit="return confirm('Are you sure you want to delete this user?')">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="text-red-600 hover:text-red-800 p-1 rounded-full hover:bg-red-50"
                                            title="Delete User">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                        </svg>
                                    </button>
                                </form>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="7" class="border border-gray-300 px-4 py-8 text-center text-gray-500">
                            No users found.
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if($users->hasPages()): ?>
            <div class="mt-6">
                <?php echo e($users->links()); ?>

            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\psdb-dashboard-backend\resources\views/users/index.blade.php ENDPATH**/ ?>