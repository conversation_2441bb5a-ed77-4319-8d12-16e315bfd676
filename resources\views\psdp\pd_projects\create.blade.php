@extends('layouts.app')

@section('title', 'Create PSDP Project')

@section('content')
<div class="py-10 px-6">
    <div class="max-w-full mx-auto bg-white rounded-xl shadow p-5 sm:p-12 sm:px-16">
        <!-- Breadcrumb -->
        <div class="flex items-center gap-2 mb-6 text-sm text-gray-600">
            <a href="{{ route('psdp.pd-dashboard') }}" class="hover:text-blue-600">PSDP Projects</a>
            <span>></span>
            <span class="text-gray-900">Create</span>
        </div>

        <!-- Header -->
        <h2 class="text-xl sm:text-2xl font-semibold text-center text-gray-900 mb-8">
            Create PSDP Project
        </h2>

        <!-- Step Indicators -->
        <div class="flex justify-center mb-8">
            <div class="flex items-center space-x-4">
                <!-- Step 1 -->
                <div class="flex items-center">
                    <div class="step-indicator active flex items-center justify-center w-10 h-10 rounded-full bg-[#1A92AA] text-white font-semibold" data-step="1">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="ml-2">
                        <p class="text-sm font-semibold text-gray-900">Project Details</p>
                        <p class="text-xs text-gray-500">Basic information, organization and timeline</p>
                    </div>
                </div>

                <!-- Connector -->
                <div class="w-16 h-px bg-gray-300"></div>

                <!-- Step 2 -->
                <div class="flex items-center">
                    <div class="step-indicator flex items-center justify-center w-10 h-10 rounded-full bg-gray-300 text-gray-600 font-semibold" data-step="2">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm-7-8a7 7 0 1114 0 7 7 0 01-14 0zm7-3a1 1 0 012 0v.092a4.535 4.535 0 011.676.662C14.082 8.043 15 8.681 15 10c0 1.319-.918 1.957-1.324 2.246a4.535 4.535 0 01-1.676.662V13a1 1 0 11-2 0v-.092a4.535 4.535 0 01-1.676-.662C7.918 11.957 7 11.319 7 10c0-1.319.918-1.957 1.324-2.246A4.535 4.535 0 0110 7.092V7z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="ml-2">
                        <p class="text-sm font-semibold text-gray-600">Budget Allocations</p>
                        <p class="text-xs text-gray-500">Financial allocations for the project</p>
                    </div>
                </div>

                <!-- Connector -->
                <div class="w-16 h-px bg-gray-300"></div>

                <!-- Step 3 -->
                <div class="flex items-center">
                    <div class="step-indicator flex items-center justify-center w-10 h-10 rounded-full bg-gray-300 text-gray-600 font-semibold" data-step="3">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                        </svg>
                    </div>
                    <div class="ml-2">
                        <p class="text-sm font-semibold text-gray-600">Deliverables & Tasks</p>
                        <p class="text-xs text-gray-500">Project deliverables and associated tasks</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form -->
        <form id="projectForm" method="POST" action="{{ route('psdp.pd-projects.store') }}" novalidate>
            @csrf
            
            <!-- Step 1: Project Details (Merged Information + Timeline) -->
            <div class="form-step active" data-step="1">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Project Information</h3>
                @if(auth()->user()->hasRole('PD'))
                    <!-- PD User: Only show Project Name (Ministry and Department are hidden) -->
                    @php
                        $userMinistry = auth()->user()->userMinistries()->with(['ministry', 'department'])->first();
                    @endphp

                    <!-- Hidden fields for PD users -->
                    @if($userMinistry)
                        <input type="hidden" name="ministry_id" value="{{ $userMinistry->ministry_id }}">
                        <input type="hidden" name="department_id" value="{{ $userMinistry->department_id }}">
                    @endif

                    <div class="grid grid-cols-1 gap-6 mb-6">
                        <div class="relative mt-4 w-full mx-auto">
                            <input type="text" name="name" id="project_name" required
                                   class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                                   placeholder="Enter project name" />
                            <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                Project Name*
                            </label>
                        </div>
                    </div>
                @else
                    <!-- Super Admin and Ministry Users -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div class="relative mt-4 w-full mx-auto">
                            <input type="text" name="name" id="project_name" required
                                   class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                                   placeholder="Enter project name" />
                            <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                Project Name*
                            </label>
                        </div>

                        @if(auth()->user()->hasRole('super admin'))
                        <!-- Ministry Dropdown for Super Admin -->
                        <div class="relative mt-4 w-full mx-auto">
                            <select name="ministry_id" id="ministry_id" required
                                    class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0 appearance-none">
                                <option value="">Select ministry</option>
                                @foreach($ministries as $ministry)
                                    <option value="{{ $ministry->id }}">{{ $ministry->name }}</option>
                                @endforeach
                            </select>
                            <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                Ministry*
                            </label>
                        </div>
                        @endif

                        @if(auth()->user()->hasRole('super admin'))
                            <!-- Super Admin: Show Ministry Dropdown -->
                            <div class="relative mt-4 w-full mx-auto">
                                <select name="ministry_id" id="ministry_id" required
                                        class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0 appearance-none">
                                    <option value="">Select ministry</option>
                                    @foreach($ministries as $ministry)
                                        <option value="{{ $ministry->id }}">{{ $ministry->name }}</option>
                                    @endforeach
                                </select>
                                <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                    Ministry*
                                </label>
                                <div class="pointer-events-none absolute inset-y-0 right-4 flex items-center text-gray-500">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                        <path d="M19 9l-7 7-7-7" />
                                    </svg>
                                </div>
                            </div>
                        @elseif(auth()->user()->hasRole('admin'))
                            @php
                                $userMinistry = auth()->user()->userMinistries()->with(['ministry', 'department'])->first();
                            @endphp

                            <!-- Hidden ministry_id for admin users -->
                            @if($userMinistry)
                                <input type="hidden" name="ministry_id" value="{{ $userMinistry->ministry_id }}">
                            @endif

                            <!-- Ministry User: Show Ministry Name (Read-only) -->
                            <div class="relative mt-4 w-full mx-auto">
                                <input type="text" value="{{ $userMinistry->ministry->name ?? 'No Ministry Assigned' }}" readonly
                                       class="block w-full text-sm text-gray-500 bg-gray-100 rounded-full border py-3 pl-4 pr-4 outline-0" />
                                <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                    Ministry
                                </label>
                            </div>
                        @endif
                    </div>

                    <!-- Show Department Dropdown for Super Admin and Ministry Users -->
                    <div class="grid grid-cols-1 gap-6 mb-6">
                        <div class="relative mt-4 w-full mx-auto">
                            <select name="department_id" id="department_id" required
                                    class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0 appearance-none">
                                <option value="">Select department</option>
                                @foreach($departments as $department)
                                    <option value="{{ $department->id }}">{{ $department->name }}</option>
                                @endforeach
                            </select>
                            <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                Department*
                            </label>
                            <div class="pointer-events-none absolute inset-y-0 right-4 flex items-center text-gray-500">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                    <path d="M19 9l-7 7-7-7" />
                                </svg>
                            </div>
                        </div>
                    </div>
                @endif

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div class="relative mt-4 w-full mx-auto">
                        <input type="text" name="psdp_no" id="psdp_no" required
                               class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                               placeholder="Enter PSDP number" />
                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                            PSDP No*
                        </label>
                    </div>

                    <div class="relative mt-4 w-full mx-auto">
                        <input type="text" name="psdp_id" id="psdp_id" required
                               class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                               placeholder="Enter PSDP ID" />
                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                            PSDP ID*
                        </label>
                    </div>
                </div>

                <div class="grid grid-cols-1 gap-6 mb-6">
                    <div class="relative mt-4 w-full mx-auto">
                        <select name="approving_authority_id" id="approving_authority_id" required
                                class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0 appearance-none">
                            <option value="">Select approving authority</option>
                            @foreach($approvingAuthorities as $authority)
                                <option value="{{ $authority->id }}">{{ $authority->name }}</option>
                            @endforeach
                        </select>
                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                            Approving Authority*
                        </label>
                        <div class="pointer-events-none absolute inset-y-0 right-4 flex items-center text-gray-500">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                <path d="M19 9l-7 7-7-7" />
                            </svg>
                        </div>
                    </div>
                </div>


                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div class="relative mt-2 w-full mx-auto">
                        <input type="date" name="authority_approved_date" id="authority_approved_date"
                               class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0" />
                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                            Authority Approved Date
                        </label>
                    </div>
                    <div class="relative mt-2 w-full mx-auto">
                        <input type="date" name="admin_approval_date" id="admin_approval_date"
                               class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0" />
                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                            Admin Approval Date
                        </label>
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div class="relative mt-2 w-full mx-auto">
                        <input type="date" name="completion_date_pc1" id="completion_date_pc1"
                               class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0" />
                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                            Completion Date PC1
                        </label>
                    </div>
                    <div class="relative mt-2 w-full mx-auto">
                        <input type="date" name="likely_completion_date" id="likely_completion_date"
                               class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0" />
                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                            Likely Completion Date
                        </label>
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <div class="relative mt-2 w-full mx-auto">
                        <input type="number" name="total_cost" id="total_cost" step="0.01" min="0"
                               class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                               placeholder="0.00" />
                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                            Total Cost (PKR)
                        </label>
                    </div>
                    <div class="relative mt-2 w-full mx-auto">
                        <input type="number" name="lc_amount" id="lc_amount" step="0.01" min="0"
                               class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                               placeholder="0.00" />
                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                            LC Amount (PKR)
                        </label>
                    </div>
                    <div class="relative mt-2 w-full mx-auto">
                        <input type="number" name="fc_amount" id="fc_amount" step="0.01" min="0"
                               class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                               placeholder="0.00" />
                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                            FC Amount (USD)
                        </label>
                    </div>
                    <div class="relative mt-2 w-full mx-auto">
                        <input type="number" name="grand_amount" id="grand_amount" step="0.01" min="0" readonly
                               class="block w-full text-sm text-gray-900 bg-gray-100 rounded-full border py-3 pl-4 pr-4 outline-0"
                               placeholder="0.00" />
                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                            Grand Amount (PKR)
                        </label>
                    </div>
                </div>

            </div>

            <!-- Step 2: Budget Allocations -->
            <div class="form-step" data-step="2" style="display: none;">
                <div id="allocations-container">
                    <div class="allocation-item border rounded-lg p-4 mb-4">
                        <h4 class="font-semibold mb-4">Financial Year Allocation</h4>
                        <div class="grid grid-cols-1 gap-6 mb-4">
                            <div class="relative mt-4 w-full mx-auto">
                                <select name="allocations[0][financial_year_id]" required class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0 appearance-none">
                                    <option value="">Select financial year</option>
                                    @foreach($financialYears as $year)
                                        <option value="{{ $year->id }}">{{ $year->name }}</option>
                                    @endforeach
                                </select>
                                <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                    Financial Year*
                                </label>
                                <div class="pointer-events-none absolute inset-y-0 right-4 flex items-center text-gray-500">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                        <path d="M19 9l-7 7-7-7" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-4">
                            <div class="relative mt-4 w-full mx-auto">
                                <input type="number" name="allocations[0][allocation_amount]" step="0.01" required
                                       class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                                       placeholder="0.00" />
                                <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                    Allocation Amount*
                                </label>
                            </div>
                            <div class="relative mt-4 w-full mx-auto">
                                <input type="number" name="allocations[0][expense_amount]" step="0.01"
                                       class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                                       placeholder="0.00" />
                                <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                    Expense Amount
                                </label>
                            </div>
                            <div class="relative mt-4 w-full mx-auto">
                                <input type="number" name="allocations[0][release_amount]" step="0.01"
                                       class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                                       placeholder="0.00" />
                                <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                    Release Amount
                                </label>
                            </div>
                        </div>

                        <!-- Quarterly Breakdown -->
                        <div class="mt-6">
                            <h5 class="text-md font-semibold text-gray-800 mb-4">Quarterly Breakdown</h5>
                            <div class="overflow-x-auto">
                                <table class="w-full border-collapse border border-gray-300 rounded-lg">
                                    <thead>
                                        <tr class="bg-gray-50">
                                            <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Quarter</th>
                                            <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Release Amount</th>
                                            <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Expense Amount</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr class="hover:bg-gray-50">
                                            <td class="border border-gray-300 px-4 py-3 text-sm font-medium text-gray-700">Q1</td>
                                            <td class="border border-gray-300 px-4 py-3">
                                                <input type="number" name="allocations[0][quarters][0][release_amount]" step="0.01"
                                                       class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                                       placeholder="0.00" />
                                            </td>
                                            <td class="border border-gray-300 px-4 py-3">
                                                <input type="number" name="allocations[0][quarters][0][expense_amount]" step="0.01"
                                                       class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                                       placeholder="0.00" />
                                            </td>
                                        </tr>
                                        <tr class="hover:bg-gray-50">
                                            <td class="border border-gray-300 px-4 py-3 text-sm font-medium text-gray-700">Q2</td>
                                            <td class="border border-gray-300 px-4 py-3">
                                                <input type="number" name="allocations[0][quarters][1][release_amount]" step="0.01"
                                                       class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                                       placeholder="0.00" />
                                            </td>
                                            <td class="border border-gray-300 px-4 py-3">
                                                <input type="number" name="allocations[0][quarters][1][expense_amount]" step="0.01"
                                                       class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                                       placeholder="0.00" />
                                            </td>
                                        </tr>
                                        <tr class="hover:bg-gray-50">
                                            <td class="border border-gray-300 px-4 py-3 text-sm font-medium text-gray-700">Q3</td>
                                            <td class="border border-gray-300 px-4 py-3">
                                                <input type="number" name="allocations[0][quarters][2][release_amount]" step="0.01"
                                                       class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                                       placeholder="0.00" />
                                            </td>
                                            <td class="border border-gray-300 px-4 py-3">
                                                <input type="number" name="allocations[0][quarters][2][expense_amount]" step="0.01"
                                                       class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                                       placeholder="0.00" />
                                            </td>
                                        </tr>
                                        <tr class="hover:bg-gray-50">
                                            <td class="border border-gray-300 px-4 py-3 text-sm font-medium text-gray-700">Q4</td>
                                            <td class="border border-gray-300 px-4 py-3">
                                                <input type="number" name="allocations[0][quarters][3][release_amount]" step="0.01"
                                                       class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                                       placeholder="0.00" />
                                            </td>
                                            <td class="border border-gray-300 px-4 py-3">
                                                <input type="number" name="allocations[0][quarters][3][expense_amount]" step="0.01"
                                                       class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                                       placeholder="0.00" />
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                    </div>
                </div>
                <button type="button" id="addAllocation" class="text-white px-4 py-2 text-sm rounded-full font-medium" style="background-color: #1A92AA;">
                    + Add Another Allocation
                </button>
            </div>

            <!-- Step 3: Deliverables & Tasks -->
            <div class="form-step" data-step="3" style="display: none;">
                <div id="deliverables-container">
                    <div class="deliverable-item border rounded-lg p-4 mb-6">
                        <div class="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
                            <h3 class="text-md font-semibold text-gray-800 whitespace-nowrap mr-4">
                                Output/Deliverable-1 :
                            </h3>
                            <div class="flex-grow hidden sm:flex h-px bg-[repeating-linear-gradient(to_right,_#ccc,_#ccc_10px,_transparent_10px,_transparent_20px)]"></div>
                            <button type="button" class="remove-deliverable text-red-600 px-4 py-2 text-sm rounded-full font-medium border border-red-600 hover:bg-red-50">
                                Remove
                            </button>
                        </div>

                        <div class="relative mt-4 w-full mx-auto mb-4">
                            <input type="text" name="deliverables[0][name]" required
                                   class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                                   placeholder="Enter deliverable name" />
                            <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                Deliverable Name*
                            </label>
                        </div>

                        <div class="tasks-container">
                            <div class="task-item border-l-4 border-blue-200 pl-4 mb-4">
                                <h5 class="font-medium mb-3">Task 1</h5>

                                <!-- Task Code and Description -->
                                <div class="flex flex-col sm:flex-row gap-6 mb-6">
                                    <div class="relative mt-4 w-full sm:w-[30%]">
                                        <input type="text" name="deliverables[0][tasks][0][code]" required
                                               class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                                               placeholder="1.1" />
                                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                            Code*
                                        </label>
                                    </div>
                                    <div class="relative mt-4 w-full sm:w-[70%]">
                                        <input type="text" name="deliverables[0][tasks][0][description]" required
                                               class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                                               placeholder="Enter task description" />
                                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                            Task Description*
                                        </label>
                                    </div>
                                </div>

                                <!-- Date Inputs -->
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                    <div class="relative mt-2 w-full mx-auto">
                                        <input type="date" name="deliverables[0][tasks][0][start_date]" required
                                               class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0" />
                                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                            Start Date*
                                        </label>
                                    </div>
                                    <div class="relative mt-2 w-full mx-auto">
                                        <input type="date" name="deliverables[0][tasks][0][end_date]" required
                                               class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0" />
                                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                            End Date*
                                        </label>
                                    </div>
                                </div>

                                <!-- Progress Inputs -->
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                    <div class="relative -mt-2 w-full mx-auto">
                                        <input type="number" name="deliverables[0][tasks][0][planned_complete]" min="0" max="100" step="0.01" required
                                               class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                                               placeholder="0" />
                                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                            Planned % Complete*
                                        </label>
                                    </div>
                                    <div class="relative -mt-2 w-full mx-auto">
                                        <input type="number" name="deliverables[0][tasks][0][actual_complete]" min="0" max="100" step="0.01" required
                                               class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                                               placeholder="0" />
                                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                            Actual % Complete*
                                        </label>
                                    </div>
                                </div>

                                <!-- Status and Remarks -->
                                <div class="flex flex-col sm:flex-row gap-6 mb-6">
                                    <div class="relative -mt-2 w-full sm:w-[30%]">
                                        <select name="deliverables[0][tasks][0][status_id]" required
                                                class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0 appearance-none">
                                            <option value="">Select status</option>
                                            @foreach($statuses as $status)
                                                <option value="{{ $status->id }}">{{ $status->name }}</option>
                                            @endforeach
                                        </select>
                                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 start-4 -translate-y-1/2 scale-75 -translate-y-6">
                                            Status*
                                        </label>
                                        <div class="pointer-events-none absolute inset-y-0 right-4 flex items-center text-gray-500">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                                <path d="M19 9l-7 7-7-7" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="relative -mt-2 w-full sm:w-[70%]">
                                        <input type="text" name="deliverables[0][tasks][0][remarks]"
                                               class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                                               placeholder="Enter remarks" />
                                        <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                                            Remarks
                                        </label>
                                    </div>
                                </div>

                                <button type="button" class="remove-task text-red-600 px-3 py-1 text-xs rounded-full font-medium border border-red-600 hover:bg-red-50 mb-4">
                                    Remove Task
                                </button>
                            </div>
                        </div>

                        <button type="button" class="add-task text-white px-4 py-2 text-sm rounded-full font-medium mb-4" style="background-color: #1A92AA;">
                            + Add Another Task
                        </button>
                    </div>
                </div>

                <button type="button" id="addDeliverable" class="text-white px-4 py-2 text-sm rounded-full font-medium" style="background-color: #1A92AA;">
                    + Add Another Deliverable
                </button>
            </div>

            <!-- Navigation Buttons -->
            <div class="flex justify-center mt-8">
                <div class="flex flex-col sm:flex-row justify-center gap-4 w-full">
                    <button type="button" id="prevBtn" class="w-full sm:w-36 text-black text-sm font-medium rounded-full px-4 py-2.5 hover:brightness-105 hidden" style="background-color: #D6DCE4;">
                        Previous
                    </button>
                    <button type="button" id="nextBtn" class="w-full sm:w-36 text-white text-sm font-medium rounded-full px-4 py-2.5 hover:brightness-110" style="background-color: #1A92AA;">
                        Next
                    </button>
                    <button type="submit" id="submitBtn" class="w-full sm:w-36 text-white text-sm font-medium rounded-full px-4 py-2.5 hover:brightness-110 hidden" style="background-color: #1A92AA;">
                        Submit
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script>
let currentStep = 1;
const totalSteps = 3;

// Initialize form
$(document).ready(function() {
    updateStepIndicators();
    updateNavigationButtons();

    // Add real-time date validation
    $('#admin_approval_date, #completion_date_pc1, #likely_completion_date').on('change', function() {
        validateDates();
    });

    // Auto-fill grand_amount when total_cost changes
    $('#total_cost').on('input', function() {
        const totalCost = parseFloat($(this).val()) || 0;
        $('#grand_amount').val(totalCost.toFixed(2));
    });


});

// Next button click
$('#nextBtn').click(function() {
    if (validateCurrentStep()) {
        if (currentStep < totalSteps) {
            currentStep++;
            showStep(currentStep);
            updateStepIndicators();
            updateNavigationButtons();
        }
    }
});

// Previous button click
$('#prevBtn').click(function() {
    if (currentStep > 1) {
        currentStep--;
        showStep(currentStep);
        updateStepIndicators();
        updateNavigationButtons();
    }
});

function showStep(step) {
    $('.form-step').removeClass('active').hide();
    $(`.form-step[data-step="${step}"]`).addClass('active').show();
}

function updateStepIndicators() {
    $('.step-indicator').each(function() {
        const stepNum = parseInt($(this).data('step'));
        if (stepNum <= currentStep) {
            $(this).removeClass('bg-gray-300 text-gray-600').addClass('bg-[#1A92AA] text-white');
            $(this).siblings().find('p').removeClass('text-gray-600').addClass('text-gray-900');
        } else {
            $(this).removeClass('bg-[#1A92AA] text-white').addClass('bg-gray-300 text-gray-600');
            $(this).siblings().find('p').removeClass('text-gray-900').addClass('text-gray-600');
        }
    });
}

function updateNavigationButtons() {
    $('#prevBtn').toggle(currentStep > 1);
    $('#nextBtn').toggle(currentStep < totalSteps);
    $('#submitBtn').toggle(currentStep === totalSteps);
}

// Date validation function
function validateDates() {
    let isValid = true;
    const adminApprovalDate = $('#admin_approval_date').val();
    const completionDatePC1 = $('#completion_date_pc1').val();
    const likelyCompletionDate = $('#likely_completion_date').val();

    // Clear previous date error messages
    $('.date-error').remove();
    $('#admin_approval_date, #completion_date_pc1, #likely_completion_date').removeClass('border-red-500');

    // Only validate if both dates are provided
    if (adminApprovalDate && completionDatePC1) {
        const adminDate = new Date(adminApprovalDate);
        const completionDate = new Date(completionDatePC1);

        // Check if dates are valid
        if (isNaN(adminDate.getTime()) || isNaN(completionDate.getTime())) {
            return isValid; // Skip validation if dates are invalid
        }

        if (completionDate <= adminDate) {
            isValid = false;
            $('#completion_date_pc1').addClass('border-red-500');
            $('#completion_date_pc1').parent().append('<div class="date-error text-red-500 text-xs mt-1">Completion Date PC1 must be greater than Admin Approval Date</div>');
        }
    }

    // Only validate if both dates are provided
    if (completionDatePC1 && likelyCompletionDate) {
        const completionDate = new Date(completionDatePC1);
        const likelyDate = new Date(likelyCompletionDate);

        // Check if dates are valid
        if (isNaN(completionDate.getTime()) || isNaN(likelyDate.getTime())) {
            return isValid; // Skip validation if dates are invalid
        }

        if (likelyDate < completionDate) {
            isValid = false;
            $('#likely_completion_date').addClass('border-red-500');
            $('#likely_completion_date').parent().append('<div class="date-error text-red-500 text-xs mt-1">Likely Completion Date must be greater than or equal to Completion Date PC1</div>');
        }
    }

    return isValid;
}

function validateCurrentStep() {
    let isValid = true;
    const currentStepElement = $(`.form-step[data-step="${currentStep}"]`);

    // Clear previous error messages
    currentStepElement.find('.error-message').remove();
    currentStepElement.find('.border-red-500').removeClass('border-red-500');

    // Validate required fields
    currentStepElement.find('input[required], select[required]').each(function() {
        if (!$(this).val()) {
            isValid = false;
            $(this).addClass('border-red-500');
            // Show error message
            if (!$(this).siblings('.error-message').length) {
                $(this).after('<span class="error-message text-red-500 text-xs mt-1">This field is required</span>');
            }
        } else {
            $(this).removeClass('border-red-500');
            $(this).siblings('.error-message').remove();
        }
    });

    // Additional date validation for step 1
    if (currentStep === 1) {
        isValid = validateDates() && isValid;
    }

    return isValid;
}

// Add allocation functionality
let allocationIndex = 1;
$('#addAllocation').click(function() {
    const allocationHtml = `
        <div class="allocation-item border rounded-lg p-4 mb-4">
            <h4 class="font-semibold mb-4">Financial Year Allocation ${allocationIndex + 1}</h4>
            <div class="grid grid-cols-1 gap-6 mb-4">
                <div class="relative mt-4 w-full mx-auto">
                    <select name="allocations[${allocationIndex}][financial_year_id]" required class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0 appearance-none">
                        <option value="">Select financial year</option>
                        @foreach($financialYears as $year)
                            <option value="{{ $year->id }}">{{ $year->name }}</option>
                        @endforeach
                    </select>
                    <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                        Financial Year*
                    </label>
                    <div class="pointer-events-none absolute inset-y-0 right-4 flex items-center text-gray-500">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                            <path d="M19 9l-7 7-7-7" />
                        </svg>
                    </div>
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-4">
                <div class="relative mt-4 w-full mx-auto">
                    <input type="number" name="allocations[${allocationIndex}][allocation_amount]" step="0.01" required
                           class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                           placeholder="0.00" />
                    <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                        Allocation Amount*
                    </label>
                </div>
                <div class="relative mt-4 w-full mx-auto">
                    <input type="number" name="allocations[${allocationIndex}][expense_amount]" step="0.01"
                           class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                           placeholder="0.00" />
                    <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                        Expense Amount
                    </label>
                </div>
                <div class="relative mt-4 w-full mx-auto">
                    <input type="number" name="allocations[${allocationIndex}][release_amount]" step="0.01"
                           class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                           placeholder="0.00" />
                    <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                        Release Amount
                    </label>
                </div>
            </div>

            <!-- Quarterly Breakdown -->
            <div class="mt-6">
                <h5 class="text-md font-semibold text-gray-800 mb-4">Quarterly Breakdown</h5>
                <div class="overflow-x-auto">
                    <table class="w-full border-collapse border border-gray-300 rounded-lg">
                        <thead>
                            <tr class="bg-gray-50">
                                <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Quarter</th>
                                <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Release Amount</th>
                                <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Expense Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="hover:bg-gray-50">
                                <td class="border border-gray-300 px-4 py-3 text-sm font-medium text-gray-700">Q1</td>
                                <td class="border border-gray-300 px-4 py-3">
                                    <input type="number" name="allocations[${allocationIndex}][quarters][0][release_amount]" step="0.01"
                                           class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                           placeholder="0.00" />
                                </td>
                                <td class="border border-gray-300 px-4 py-3">
                                    <input type="number" name="allocations[${allocationIndex}][quarters][0][expense_amount]" step="0.01"
                                           class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                           placeholder="0.00" />
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="border border-gray-300 px-4 py-3 text-sm font-medium text-gray-700">Q2</td>
                                <td class="border border-gray-300 px-4 py-3">
                                    <input type="number" name="allocations[${allocationIndex}][quarters][1][release_amount]" step="0.01"
                                           class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                           placeholder="0.00" />
                                </td>
                                <td class="border border-gray-300 px-4 py-3">
                                    <input type="number" name="allocations[${allocationIndex}][quarters][1][expense_amount]" step="0.01"
                                           class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                           placeholder="0.00" />
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="border border-gray-300 px-4 py-3 text-sm font-medium text-gray-700">Q3</td>
                                <td class="border border-gray-300 px-4 py-3">
                                    <input type="number" name="allocations[${allocationIndex}][quarters][2][release_amount]" step="0.01"
                                           class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                           placeholder="0.00" />
                                </td>
                                <td class="border border-gray-300 px-4 py-3">
                                    <input type="number" name="allocations[${allocationIndex}][quarters][2][expense_amount]" step="0.01"
                                           class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                           placeholder="0.00" />
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="border border-gray-300 px-4 py-3 text-sm font-medium text-gray-700">Q4</td>
                                <td class="border border-gray-300 px-4 py-3">
                                    <input type="number" name="allocations[${allocationIndex}][quarters][3][release_amount]" step="0.01"
                                           class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                           placeholder="0.00" />
                                </td>
                                <td class="border border-gray-300 px-4 py-3">
                                    <input type="number" name="allocations[${allocationIndex}][quarters][3][expense_amount]" step="0.01"
                                           class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                           placeholder="0.00" />
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <button type="button" class="remove-allocation text-red-600 px-3 py-1 text-xs rounded-full font-medium border border-red-600 hover:bg-red-50">
                Remove Allocation
            </button>
        </div>
    `;
    $('#allocations-container').append(allocationHtml);
    allocationIndex++;
});

// Remove allocation
$(document).on('click', '.remove-allocation', function() {
    $(this).closest('.allocation-item').remove();
});

// Add deliverable functionality
let deliverableIndex = 1;
$('#addDeliverable').click(function() {
    const deliverableHtml = `
        <div class="deliverable-item border rounded-lg p-4 mb-6">
            <div class="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
                <h3 class="text-md font-semibold text-gray-800 whitespace-nowrap mr-4">
                    Output/Deliverable-${deliverableIndex + 1} :
                </h3>
                <div class="flex-grow hidden sm:flex h-px bg-[repeating-linear-gradient(to_right,_#ccc,_#ccc_10px,_transparent_10px,_transparent_20px)]"></div>
                <button type="button" class="remove-deliverable text-red-600 px-4 py-2 text-sm rounded-full font-medium border border-red-600 hover:bg-red-50">
                    Remove
                </button>
            </div>

            <div class="relative mt-4 w-full mx-auto mb-4">
                <input type="text" name="deliverables[${deliverableIndex}][name]" required
                       class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                       placeholder="Enter deliverable name" />
                <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                    Deliverable Name*
                </label>
            </div>

            <div class="tasks-container">
                <div class="task-item border-l-4 border-blue-200 pl-4 mb-4">
                    <h5 class="font-medium mb-3">Task 1</h5>
                    ${getTaskHtml(deliverableIndex, 0)}
                    <button type="button" class="remove-task text-red-600 px-3 py-1 text-xs rounded-full font-medium border border-red-600 hover:bg-red-50 mb-4">
                        Remove Task
                    </button>
                </div>
            </div>

            <button type="button" class="add-task text-white px-4 py-2 text-sm rounded-full font-medium mb-4" style="background-color: #1A92AA;">
                + Add Another Task
            </button>
        </div>
    `;
    $('#deliverables-container').append(deliverableHtml);
    deliverableIndex++;
});

// Remove deliverable
$(document).on('click', '.remove-deliverable', function() {
    $(this).closest('.deliverable-item').remove();
});

// Add task functionality
$(document).on('click', '.add-task', function() {
    const deliverableItem = $(this).closest('.deliverable-item');
    const deliverableIdx = deliverableItem.index();
    const tasksContainer = deliverableItem.find('.tasks-container');
    const taskCount = tasksContainer.find('.task-item').length;

    const taskHtml = `
        <div class="task-item border-l-4 border-blue-200 pl-4 mb-4">
            <h5 class="font-medium mb-3">Task ${taskCount + 1}</h5>
            ${getTaskHtml(deliverableIdx, taskCount)}
            <button type="button" class="remove-task text-red-600 px-3 py-1 text-xs rounded-full font-medium border border-red-600 hover:bg-red-50 mb-4">
                Remove Task
            </button>
        </div>
    `;
    tasksContainer.append(taskHtml);
});

// Remove task
$(document).on('click', '.remove-task', function() {
    const taskItem = $(this).closest('.task-item');
    const tasksContainer = taskItem.closest('.tasks-container');

    if (tasksContainer.find('.task-item').length > 1) {
        taskItem.remove();
        // Update task numbers
        tasksContainer.find('.task-item').each(function(index) {
            $(this).find('h5').text(`Task ${index + 1}`);
        });
    } else {
        alert('At least one task is required per deliverable.');
    }
});

function getTaskHtml(deliverableIndex, taskIndex) {
    return `
        <!-- Task Code and Description -->
        <div class="flex flex-col sm:flex-row gap-6 mb-6">
            <div class="relative mt-4 w-full sm:w-[30%]">
                <input type="text" name="deliverables[${deliverableIndex}][tasks][${taskIndex}][code]" required
                       class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                       placeholder="1.1" />
                <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                    Code*
                </label>
            </div>
            <div class="relative mt-4 w-full sm:w-[70%]">
                <input type="text" name="deliverables[${deliverableIndex}][tasks][${taskIndex}][description]" required
                       class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                       placeholder="Enter task description" />
                <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                    Task Description*
                </label>
            </div>
        </div>

        <!-- Date Inputs -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div class="relative mt-2 w-full mx-auto">
                <input type="date" name="deliverables[${deliverableIndex}][tasks][${taskIndex}][start_date]" required
                       class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0" />
                <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                    Start Date*
                </label>
            </div>
            <div class="relative mt-2 w-full mx-auto">
                <input type="date" name="deliverables[${deliverableIndex}][tasks][${taskIndex}][end_date]" required
                       class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0" />
                <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                    End Date*
                </label>
            </div>
        </div>

        <!-- Progress Inputs -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div class="relative -mt-2 w-full mx-auto">
                <input type="number" name="deliverables[${deliverableIndex}][tasks][${taskIndex}][planned_complete]" min="0" max="100" step="0.01" required
                       class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                       placeholder="0" />
                <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                    Planned % Complete*
                </label>
            </div>
            <div class="relative -mt-2 w-full mx-auto">
                <input type="number" name="deliverables[${deliverableIndex}][tasks][${taskIndex}][actual_complete]" min="0" max="100" step="0.01" required
                       class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                       placeholder="0" />
                <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                    Actual % Complete*
                </label>
            </div>
        </div>

        <!-- Status and Remarks -->
        <div class="flex flex-col sm:flex-row gap-6 mb-6">
            <div class="relative -mt-2 w-full sm:w-[30%]">
                <select name="deliverables[${deliverableIndex}][tasks][${taskIndex}][status_id]" required
                        class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0 appearance-none">
                    <option value="">Select status</option>
                    @foreach($statuses as $status)
                        <option value="{{ $status->id }}">{{ $status->name }}</option>
                    @endforeach
                </select>
                <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 start-4 -translate-y-1/2 scale-75 -translate-y-6">
                    Status*
                </label>
                <div class="pointer-events-none absolute inset-y-0 right-4 flex items-center text-gray-500">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                        <path d="M19 9l-7 7-7-7" />
                    </svg>
                </div>
            </div>
            <div class="relative -mt-2 w-full sm:w-[70%]">
                <input type="text" name="deliverables[${deliverableIndex}][tasks][${taskIndex}][remarks]"
                       class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                       placeholder="Enter remarks" />
                <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                    Remarks
                </label>
            </div>
        </div>
    `;
}

// Cascading Dropdowns for Ministry -> Department (only for Super Admin)
@if(auth()->user()->hasRole('super admin'))
$('#ministry_id').change(function() {
    const ministryId = $(this).val();
    const departmentSelect = $('#department_id');

    // Clear department dropdown
    departmentSelect.html('<option value="">Select department</option>');

    if (ministryId) {
        // Load departments for selected ministry via AJAX
        $.get(`/psdp/api/pd/departments/${ministryId}`)
            .done(function(departments) {
                departments.forEach(department => {
                    departmentSelect.append(`<option value="${department.id}">${department.name}</option>`);
                });
            })
            .fail(function() {
                console.error('Failed to load departments');
            });
    }
});
@endif

// Auto-populate departments for admin users on page load
@if(auth()->user()->hasRole('admin'))
$(document).ready(function() {
    @php
        $userMinistry = auth()->user()->userMinistries()->with(['ministry', 'department'])->first();
    @endphp

    console.log('Admin user ministry data:', @json($userMinistry));

    @if($userMinistry)
    const ministryId = {{ $userMinistry->ministry_id }};
    const departmentSelect = $('#department_id');

    console.log('Loading departments for ministry ID:', ministryId);

    if (ministryId) {
        // Load departments for admin user's ministry
        $.get(`/psdp/api/pd/departments/${ministryId}`)
            .done(function(departments) {
                console.log('Departments loaded:', departments);
                departmentSelect.html('<option value="">Select department</option>');
                departments.forEach(department => {
                    departmentSelect.append(`<option value="${department.id}">${department.name}</option>`);
                });
            })
            .fail(function(xhr, status, error) {
                console.error('Failed to load departments for admin user:', error);
                console.error('Response:', xhr.responseText);
            });
    }
    @else
    console.log('No ministry assignment found for admin user');
    @endif
});
@endif

// Function to validate all steps
function validateAllSteps() {
    let isValid = true;

    // Validate all steps
    for (let step = 1; step <= totalSteps; step++) {
        const stepElement = $(`.form-step[data-step="${step}"]`);

        // Clear previous error messages
        stepElement.find('.error-message, .date-error').remove();
        stepElement.find('.border-red-500').removeClass('border-red-500');

        // Validate required fields
        stepElement.find('input[required], select[required]').each(function() {
            if (!$(this).val()) {
                isValid = false;
                $(this).addClass('border-red-500');
                $(this).after('<span class="error-message text-red-500 text-xs mt-1">This field is required</span>');
            }
        });

        // Validate dates for step 1
        if (step === 1) {
            if (!validateDates()) {
                isValid = false;
            }
        }
    }

    return isValid;
}

// Form submission
$('#projectForm').submit(function(e) {
    e.preventDefault();

    console.log('Form submission started');

    if (!validateAllSteps()) {
        console.log('Validation failed');
        // If validation fails, go to the first step with errors
        for (let step = 1; step <= totalSteps; step++) {
            const stepElement = $(`.form-step[data-step="${step}"]`);
            if (stepElement.find('.error-message, .date-error, .border-red-500').length > 0) {
                currentStep = step;
                showStep(currentStep);
                updateStepIndicators();
                updateNavigationButtons();
                break;
            }
        }
        return;
    }

    console.log('Validation passed, submitting form');

    // Show loading state
    const submitBtn = $('#submitBtn');
    const originalText = submitBtn.text();
    submitBtn.prop('disabled', true).text('Creating...');

    // Clear previous errors
    $('.error-message').remove();
    $('.border-red-500').removeClass('border-red-500');

    // Collect form data
    const formData = new FormData(this);

    // Debug form data
    console.log('Form data collected:');
    for (let [key, value] of formData.entries()) {
        console.log(key, value);
    }

    // Submit via AJAX
    $.ajax({
        url: $(this).attr('action'),
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            console.log('Success response:', response);
            submitBtn.prop('disabled', false).text(originalText);

            if (response.success) {
                alert('Project created successfully!');
                window.location.href = response.redirect || "{{ route('psdp.pd-projects.index') }}";
            }
        },
        error: function(xhr) {
            console.log('Error response:', xhr);
            console.log('Response JSON:', xhr.responseJSON);
            console.log('Response Text:', xhr.responseText);
            submitBtn.prop('disabled', false).text(originalText);

            if (xhr.status === 422) {
                // Validation errors
                const errors = xhr.responseJSON?.errors;
                if (errors) {
                    // Clear previous errors
                    $('.error-message').remove();
                    $('.border-red-500').removeClass('border-red-500');

                    Object.keys(errors).forEach(function(key) {
                        const input = $(`[name="${key}"]`);
                        input.addClass('border-red-500');
                        if (!input.siblings('.error-message').length) {
                            input.after(`<span class="error-message text-red-500 text-xs mt-1">${errors[key][0]}</span>`);
                        }
                    });

                    alert('Please fix the validation errors and try again.');
                }
            } else {
                // Server error
                const message = xhr.responseJSON?.message || 'An error occurred while creating the project.';
                alert('Error: ' + message);
            }
        }
    });
});
</script>
@endpush
