<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.jsdelivr.net/npm/flowbite@3.1.2/dist/flowbite.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script src="https://cdn.jsdelivr.net/npm/flowbite@3.1.2/dist/flowbite.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <title>MOITT</title>
</head>
<body class="bg-[#ebf3f8]">
    <?php include "./common/header.html" ?>

    <h2 class="text-[24px] text-[#0D163A] font-semibold ps-5 mt-5">PSDP Management Dashboard</h2>

    <div class="flex items-center space-x-4 p-5 mt-5">
                <!-- Search input -->
                <div class="flex items-center bg-white rounded-full px-4 py-2 w-[80%]">
                <img src="./assets/search-normal.svg" alt="">
                <input
                    type="text"
                    placeholder="Search here..."
                    class="bg-transparent focus:outline-none outline-none focus:ring-0 border-0 w-full text-sm text-gray-700"
                />
                </div>
                <!-- Sort button -->
                 <div class="w-[20%]">
                <button class="bg-white w-full text-gray-500 text-sm rounded-full px-4 py-4 ">
                Sort by: <span class="font-semibold text-gray-700">Department</span>
                </button>
                </div>
            </div>
    
    <div class="p-5 flex flex-col gap-10">
        <div class=" w-full bg-white rounded-md p-5 flex gap-10">

            <div class="w-[50%] flex flex-col justify-around gap-5">
                <div>
                    <p class="text-[#0D163A] text-[20px] font-semibold">Smart Office</p>

                    <p class="text-[#0D163A] text-[16px] mt-5">Federal Ministries & Departments PSDP through MoITT (NITB)</p>
                </div>

                <div class="w-full">
                    <table class="w-full border-separate border-spacing-y-[15px]">
                        <thead class="text-left">
                            <th>Total Budget</th>
                            <th>Allocated</th>
                            <th>Expenditure</th>
                            <th>Cumulative Exp</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Rs. 572.80 M</td>
                                <td>Rs. 300.00 M</td>
                                <td>Rs. 177.86 M</td>
                                <td>Rs. 281.41 M</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

            </div>

            
            <div class="w-[50%]">
                <div class="w-full bg-[#EBF3F8] p-[20px] rounded-[18px] h-full">
                    <div class="flex justify-between items-center pb-5 border-b border-[#B6B6B6]">
                        <div class="flex items-center gap-3">
                            <img src="./assets/coin-icon.svg" alt="">
                            <p class="text-[18px] font-semibold">Project Status</p>
                        </div>

                        <div class="flex items-center gap-3">

                            <img src="./assets/arrow-chart2.svg" class="cursor-pointer">

                            <p class="text-[10px] font-semibold">View Details</p>

                        </div>
                        
                    </div>

                    <div class="flex justify-between items-center mt-5">

                    <div class="flex flex-col gap-3">
                        <p class="text-[10px] font-semibold">Utilization against amount released</p>
                        <button class="cursor-pointer bg-[#1A92AA] text-white px-8 py-3 rounded-full text-[18px] font-semibold">61.5% Utilization</button>
                    </div>

                    <div id="chart2" class="w-[112px] h-[60px]"></div>
                    
                    </div>
                    
                </div>
            </div>
            
        </div>



        <div class=" w-full bg-white rounded-md p-5 flex gap-10">

            <div class="w-[50%] flex flex-col justify-around gap-5">
                <div>
                    <p class="text-[#0D163A] text-[20px] font-semibold">Hybrid Power Solution (Solarization) for Remote Sites in GB</p>

                    <p class="text-[#0D163A] text-[16px] mt-5">PSEB</p>
                </div>

                <div class="w-full">
                    <table class="w-full border-separate border-spacing-y-[15px]">
                        <thead class="text-left">
                            <th>Total Budget</th>
                            <th>Allocated</th>
                            <th>Expenditure</th>
                            <th>Cumulative Exp</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Rs. 572.80 M</td>
                                <td>Rs. 300.00 M</td>
                                <td>Rs. 177.86 M</td>
                                <td>Rs. 281.41 M</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

            </div>

            
            <div class="w-[50%]">
                <div class="w-full bg-[#F8EBED] p-[20px] rounded-[18px] h-full">
                    <div class="flex justify-between items-center pb-5 border-b border-[#B6B6B6]">
                        <div class="flex items-center gap-3">
                            <img src="./assets/coin-icon-red.svg" alt="">
                            <p class="text-[18px] font-semibold">Project Status</p>
                        </div>

                        <div class="flex items-center gap-3">

                        <div class="w-[34px] h-[34px] cursor-pointer rounded-full bg-[#EEDBDF] flex justify-center items-center">
                            <img src="./assets/arrow-red.svg">
                        </div>

                            <p class="text-[10px] font-semibold">View Details</p>

                        </div>
                        
                    </div>

                    <div class="flex justify-between items-center mt-5">

                    <div class="flex flex-col gap-3">
                        <p class="text-[10px] font-semibold">Utilization against amount released</p>
                        <button class="cursor-pointer bg-[#DB576D] text-white px-8 py-3 rounded-full text-[18px] font-semibold">58% Utilization</button>
                    </div>

                    <div id="chart4" class="w-[112px] h-[60px]"></div>
                    
                    </div>
                    
                </div>
            </div>
            
        </div>


        <div class=" w-full bg-white rounded-md p-5 flex gap-10">

            <div class="w-[50%] flex flex-col justify-around gap-5">
                <div>
                    <p class="text-[#0D163A] text-[20px] font-semibold">One Patient One ID</p>

                    <p class="text-[#0D163A] text-[16px] mt-5">NITB</p>
                </div>

                <div class="w-full">
                    <table class="w-full border-separate border-spacing-y-[15px]">
                        <thead class="text-left">
                            <th>Total Budget</th>
                            <th>Allocated</th>
                            <th>Expenditure</th>
                            <th>Cumulative Exp</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Rs. 572.80 M</td>
                                <td>Rs. 300.00 M</td>
                                <td>Rs. 177.86 M</td>
                                <td>Rs. 281.41 M</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

            </div>

            
            <div class="w-[50%]">
                <div class="w-full bg-[#EBF3F8] p-[20px] rounded-[18px] h-full">
                    <div class="flex justify-between items-center pb-5 border-b border-[#B6B6B6]">
                        <div class="flex items-center gap-3">
                            <img src="./assets/coin-icon.svg" alt="">
                            <p class="text-[18px] font-semibold">Project Status</p>
                        </div>

                        <div class="flex items-center gap-3">

                            <img src="./assets/arrow-chart2.svg" class="cursor-pointer">

                            <p class="text-[10px] font-semibold">View Details</p>

                        </div>
                        
                    </div>

                    <div class="flex justify-between items-center mt-5">

                    <div class="flex flex-col gap-3">
                        <p class="text-[10px] font-semibold">Utilization against amount released</p>
                        <button class="cursor-pointer bg-[#1A92AA] text-white px-8 py-3 rounded-full text-[18px] font-semibold">58% Utilization</button>
                    </div>

                    <div id="chart5" class="w-[112px] h-[60px]"></div>
                    
                    </div>
                    
                </div>
            </div>
            
        </div>


        <div class=" w-full bg-white rounded-md p-5 flex gap-10">

            <div class="w-[50%] flex flex-col justify-around gap-5">
                <div>
                    <p class="text-[#0D163A] text-[20px] font-semibold">Crime Analytics and Smart Policing</p>

                    <p class="text-[#0D163A] text-[16px] mt-5">MoITT</p>
                </div>

                <div class="w-full">
                    <table class="w-full border-separate border-spacing-y-[15px]">
                        <thead class="text-left">
                            <th>Total Budget</th>
                            <th>Allocated</th>
                            <th>Expenditure</th>
                            <th>Cumulative Exp</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Rs. 572.80 M</td>
                                <td>Rs. 300.00 M</td>
                                <td>Rs. 177.86 M</td>
                                <td>Rs. 281.41 M</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

            </div>

            
            <div class="w-[50%]">
                <div class="w-full bg-[#EBF3F8] p-[20px] rounded-[18px] h-full">
                    <div class="flex justify-between items-center pb-5 border-b border-[#B6B6B6]">
                        <div class="flex items-center gap-3">
                            <img src="./assets/coin-icon.svg" alt="">
                            <p class="text-[18px] font-semibold">Project Status</p>
                        </div>

                        <div class="flex items-center gap-3">

                            <img src="./assets/arrow-chart2.svg" class="cursor-pointer">

                            <p class="text-[10px] font-semibold">View Details</p>

                        </div>
                        
                    </div>

                    <div class="flex justify-between items-center mt-5">

                    <div class="flex flex-col gap-3">
                        <p class="text-[10px] font-semibold">Utilization against amount released</p>
                        <button class="cursor-pointer bg-[#1A92AA] text-white px-8 py-3 rounded-full text-[18px] font-semibold">58% Utilization</button>
                    </div>

                    <div id="chart6" class="w-[112px] h-[60px]"></div>
                    
                    </div>
                    
                </div>
            </div>
            
        </div>


    </div>


    <!-- Paginantion Start -->
    
    <div class="p-5 w-full flex justify-end items-center">
        <nav aria-label="Page navigation example">
            <ul class="flex items-center -space-x-px h-8 text-sm">
                <li>
                <a
                    href="#"
                    class="flex items-center justify-center px-3 h-8 ms-0 leading-tight text-gray-500 bg-white border border-e-0 border-gray-300 rounded-s-lg hover:bg-gray-100 hover:text-gray-700"
                >
                    <span class="sr-only">Previous</span>
                    <svg
                    class="w-2.5 h-2.5 rtl:rotate-180"
                    aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 6 10"
                    >
                    <path
                        stroke="currentColor"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M5 1 1 5l4 4"
                    />
                    </svg>
                </a>
                </li>
                <li>
                <a
                    href="#"
                    class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500  border border-gray-300 hover:bg-gray-100 hover:text-gray-700"
                >
                    1
                </a>
                </li>
                <li>
                <a
                    href="#"
                    class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500  border border-gray-300 hover:bg-gray-100 hover:text-gray-700"
                >
                    2
                </a>
                </li>
                <li>
                <a
                    href="#"
                    aria-current="page"
                    class="z-10 flex items-center justify-center px-3 h-8 leading-tight text-blue-600 border border-blue-300 bg-blue-50 hover:bg-blue-100 hover:text-blue-700"
                >
                    3
                </a>
                </li>
                <li>
                <a
                    href="#"
                    class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500  border border-gray-300 hover:bg-gray-100 hover:text-gray-700"
                >
                    4
                </a>
                </li>
                <li>
                <a
                    href="#"
                    class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500  border border-gray-300 hover:bg-gray-100 hover:text-gray-700"
                >
                    5
                </a>
                </li>
                <li>
                <a
                    href="#"
                    class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 rounded-e-lg hover:bg-gray-100 hover:text-gray-700"
                >
                    <span class="sr-only">Next</span>
                    <svg
                    class="w-2.5 h-2.5 rtl:rotate-180"
                    aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 6 10"
                    >
                    <path
                        stroke="currentColor"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="m1 9 4-4-4-4"
                    />
                    </svg>
                </a>
                </li>
            </ul>
            </nav>

    </div>

    <!-- Paginantion End -->



    <!-- Charts -->

<script>
// Second chart (chart2) - without numbering at breakpoints
const options2 = {
    series: [{
        name: 'Data Series',
        data: [30, 45, 39, 55]
    }],
    chart: {
        type: 'area',
        height: 100,
        toolbar: {
            show: false
        },
        zoom: {
            enabled: false
        }
    },
    stroke: {
        curve: 'smooth',
        width: 3,
        colors: ['#14B8A6']
    },
    fill: {
        type: 'gradient',
        gradient: {
            shade: 'light',
            type: 'vertical',
            shadeIntensity: 0.5,
            gradientToColors: ['transparent'],
            inverseColors: false,
            opacityFrom: 0.4,
            opacityTo: 0,
            stops: [0, 100]
        }
    },
    colors: ['#14B8A6'],
    grid: {
        show: false
    },
    xaxis: {
        labels: {
            show: false
        },
        axisBorder: {
            show: false
        },
        axisTicks: {
            show: false
        },
        categories: [1, 2, 3] // Hidden categories for data points
    },
    yaxis: {
        labels: {
            show: false
        }
    },
    tooltip: {
        enabled: true // Disabled tooltip to remove any numbering on hover
    },
    markers: {
        size: 0
    },
    dataLabels: {
        enabled: false // Ensures no data labels are shown
    }
};

const chart2 = new ApexCharts(document.querySelector("#chart2"), options2);
chart2.render();
</script>





<script>
// Fourth chart (chart4) - same as chart2
const options4 = {
    series: [{
        name: 'Data Series',
        data: [80, 50, 55, 40]
    }],
    chart: {
        type: 'area',
        height: 100,
        toolbar: {
            show: false
        },
        zoom: {
            enabled: false
        }
    },
    stroke: {
        curve: 'smooth',
        width: 3,
        colors: ['#DB576D']
    },
    fill: {
        type: 'gradient',
        gradient: {
            shade: 'light',
            type: 'vertical',
            shadeIntensity: 0.5,
            gradientToColors: ['transparent'],
            inverseColors: false,
            opacityFrom: 0.4,
            opacityTo: 0,
            stops: [0, 100]
        }
    },
    colors: ['#E97070B2'],
    grid: {
        show: false
    },
    xaxis: {
        labels: {
            show: false
        },
        axisBorder: {
            show: false
        },
        axisTicks: {
            show: false
        },
        categories: [1, 2, 3, 4] // Updated to match 4 data points
    },
    yaxis: {
        labels: {
            show: false
        }
    },
    tooltip: {
        enabled: true
    },
    markers: {
        size: 0
    },
    dataLabels: {
        enabled: false
    }
};

const chart4 = new ApexCharts(document.querySelector("#chart4"), options4);
chart4.render();
</script>



<script>
// Fourth chart (chart4) - same as chart2
const options5 = {
    series: [{
        name: 'Data Series',
        data: [30, 45, 39, 55]
    }],
    chart: {
        type: 'area',
        height: 100,
        toolbar: {
            show: false
        },
        zoom: {
            enabled: false
        }
    },
    stroke: {
        curve: 'smooth',
        width: 3,
        colors: ['#14B8A6']
    },
    fill: {
        type: 'gradient',
        gradient: {
            shade: 'light',
            type: 'vertical',
            shadeIntensity: 0.5,
            gradientToColors: ['transparent'],
            inverseColors: false,
            opacityFrom: 0.4,
            opacityTo: 0,
            stops: [0, 100]
        }
    },
    colors: ['#14B8A6'],
    grid: {
        show: false
    },
    xaxis: {
        labels: {
            show: false
        },
        axisBorder: {
            show: false
        },
        axisTicks: {
            show: false
        },
        categories: [1, 2, 3, 4] // Updated to match 4 data points
    },
    yaxis: {
        labels: {
            show: false
        }
    },
    tooltip: {
        enabled: true
    },
    markers: {
        size: 0
    },
    dataLabels: {
        enabled: false
    }
};

const chart5 = new ApexCharts(document.querySelector("#chart5"), options5);
chart5.render();
</script>


<script>
// Fourth chart (chart4) - same as chart2
const options6 = {
    series: [{
        name: 'Data Series',
        data: [30, 45, 39, 55]
    }],
    chart: {
        type: 'area',
        height: 100,
        toolbar: {
            show: false
        },
        zoom: {
            enabled: false
        }
    },
    stroke: {
        curve: 'smooth',
        width: 3,
        colors: ['#14B8A6']
    },
    fill: {
        type: 'gradient',
        gradient: {
            shade: 'light',
            type: 'vertical',
            shadeIntensity: 0.5,
            gradientToColors: ['transparent'],
            inverseColors: false,
            opacityFrom: 0.4,
            opacityTo: 0,
            stops: [0, 100]
        }
    },
    colors: ['#14B8A6'],
    grid: {
        show: false
    },
    xaxis: {
        labels: {
            show: false
        },
        axisBorder: {
            show: false
        },
        axisTicks: {
            show: false
        },
        categories: [1, 2, 3, 4] // Updated to match 4 data points
    },
    yaxis: {
        labels: {
            show: false
        }
    },
    tooltip: {
        enabled: true
    },
    markers: {
        size: 0
    },
    dataLabels: {
        enabled: false
    }
};

const chart6 = new ApexCharts(document.querySelector("#chart6"), options6);
chart6.render();
</script>
    
</body>
</html>