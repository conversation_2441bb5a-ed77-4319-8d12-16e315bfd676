<?php

namespace App\Models\PSDP;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Department extends Model
{
    protected $fillable = [
        'ministry_id',
        'division_id',
        'name',
        'address',
        'phone',
        'isActive',
        'isAut'
    ];

    protected $casts = [
        'isActive' => 'boolean',
        'isAut' => 'boolean'
    ];

    /**
     * Get the ministry that owns the department.
     */
    public function ministry(): BelongsTo
    {
        return $this->belongsTo(Ministry::class, 'ministry_id');
    }

    /**
     * Get the division that owns the department.
     */
    public function division(): BelongsTo
    {
        return $this->belongsTo(Division::class, 'division_id');
    }

    /**
     * Get the projects for this department.
     */
    public function projects(): HasMany
    {
        return $this->hasMany(Project::class);
    }

    /**
     * Get the user ministries for this department.
     */
    public function userMinistries(): Has<PERSON><PERSON>
    {
        return $this->hasMany(\App\Models\PSDP\UserMinistry::class);
    }

    /**
     * Get the users assigned to this department.
     */
    public function users()
    {
        return $this->belongsToMany(\App\Models\User::class, 'user_ministries')
                    ->withPivot('ministry_id', 'is_active')
                    ->withTimestamps();
    }
}
