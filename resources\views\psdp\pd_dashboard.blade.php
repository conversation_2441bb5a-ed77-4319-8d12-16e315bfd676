@extends('layouts.app')

@section('title', 'PD Dashboard')

@section('content')
<div class="py-10 px-6">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <h1 class="text-2xl font-bold text-gray-900">PD Dashboard</h1>
            <a href="{{ route('psdp.pd-projects.create') }}" 
               class="bg-[#1A92AA] text-white px-6 py-3 rounded-full font-medium hover:brightness-110">
                + Create New Project
            </a>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100">
                        <img src="{{ asset('assets/chart-state.svg') }}" alt="" class="w-6 h-6">
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Projects</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $totalProjects }}</p>
                    </div>
                </div>
            </div>

            @foreach($projectsByStatus as $status)
            <div class="bg-white rounded-xl shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100">
                        <img src="{{ asset('assets/approval-icon.svg') }}" alt="" class="w-6 h-6">
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">{{ $status->status }}</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $status->count }}</p>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <!-- Projects by Department -->
        <div class="bg-white rounded-xl shadow p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Projects by Department</h2>
            <div class="space-y-4">
                @foreach($projectsByDepartment as $dept)
                <div class="flex items-center justify-between">
                    <span class="text-gray-700">{{ $dept->department }}</span>
                    <span class="bg-[#1A92AA] text-white px-3 py-1 rounded-full text-sm">{{ $dept->count }}</span>
                </div>
                @endforeach
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-xl shadow p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <a href="{{ route('psdp.pd-projects.index') }}" 
                   class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <img src="{{ asset('assets/chart-state.svg') }}" alt="" class="w-8 h-8 mr-3">
                    <div>
                        <p class="font-medium text-gray-900">View All Projects</p>
                        <p class="text-sm text-gray-600">Manage and monitor projects</p>
                    </div>
                </a>
                
                <a href="{{ route('psdp.pd-projects.create') }}" 
                   class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <img src="{{ asset('assets/coin-icon.svg') }}" alt="" class="w-8 h-8 mr-3">
                    <div>
                        <p class="font-medium text-gray-900">Create Project</p>
                        <p class="text-sm text-gray-600">Add new PSDP project</p>
                    </div>
                </a>
                
                <a href="#" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <img src="{{ asset('assets/export.svg') }}" alt="" class="w-8 h-8 mr-3">
                    <div>
                        <p class="font-medium text-gray-900">Export Reports</p>
                        <p class="text-sm text-gray-600">Generate project reports</p>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>
@endsection
