# User Ministries Implementation

## Overview
Implemented the `user_ministries` lookup table to establish relationships between users, ministries, and departments, allowing for proper access control and organizational hierarchy.

## Database Structure

### user_ministries Table
```sql
CREATE TABLE user_ministries (
  id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  user_id bigint(20) unsigned NOT NULL,
  ministry_id bigint(20) unsigned NOT NULL,
  department_id bigint(20) unsigned NULL,
  is_active tinyint(1) DEFAULT 1,
  created_at timestamp NULL DEFAULT NULL,
  updated_at timestamp NULL DEFAULT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY unique_user_ministry_dept (user_id, ministry_id, department_id),
  FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
  FOREIGN KEY (ministry_id) REFERENCES minestries (id) ON DELETE CASCADE,
  FOREIGN KEY (department_id) REFERENCES departments (id) ON DELETE CASCADE
);
```

## Models and Relationships

### UserMinistry Model (`App\Models\PSDP\UserMinistry`)
- **Table**: `user_ministries`
- **Fillable**: `user_id`, `ministry_id`, `department_id`, `is_active`
- **Relationships**:
  - `belongsTo(User::class)` - user
  - `belongsTo(Ministry::class)` - ministry
  - `belongsTo(Department::class)` - department

### Updated User Model
**New Relationships Added**:
- `hasMany(UserMinistry::class)` - userMinistries
- `belongsToMany(Ministry::class)` - ministries (through user_ministries)
- `belongsToMany(Department::class)` - departments (through user_ministries)

### Updated Ministry Model
**New Relationships Added**:
- `hasMany(UserMinistry::class)` - userMinistries
- `belongsToMany(User::class)` - users (through user_ministries)

### Updated Department Model
**New Relationships Added**:
- `hasMany(UserMinistry::class)` - userMinistries
- `belongsToMany(User::class)` - users (through user_ministries)

## User Assignments in Seeder

### Current User Assignments

#### Ministry User (Admin Role)
- **User**: <EMAIL>
- **Ministry**: Ministry of Information Technology and Telecommunication (ID: 16)
- **Department**: null (can access all departments in the ministry)
- **Access Level**: Full ministry access

#### PD User (Project Director Role)
- **User**: <EMAIL>
- **Ministry**: Ministry of Information Technology and Telecommunication (ID: 16)
- **Department**: National Information Technology Board (NITB) (ID: 198)
- **Access Level**: Specific department access

#### Super Admin User
- **User**: <EMAIL>
- **No ministry assignment**: Has system-wide access through role permissions

## Key Features

### 1. Hierarchical Access Control
- **Ministry Level Access**: Users can be assigned to entire ministries
- **Department Level Access**: Users can be assigned to specific departments
- **Flexible Assignment**: One user can be assigned to multiple ministries/departments

### 2. Data Integrity
- **Unique Constraint**: Prevents duplicate assignments (user_id, ministry_id, department_id)
- **Cascade Deletes**: Maintains referential integrity
- **Active Status**: Allows for soft enabling/disabling of assignments

### 3. Relationship Queries
```php
// Get all ministries for a user
$user->ministries;

// Get all departments for a user
$user->departments;

// Get all users in a ministry
$ministry->users;

// Get all users in a department
$department->users;

// Get active assignments only
$user->userMinistries()->where('is_active', true)->get();
```

## Usage Examples

### Check User Access
```php
// Check if user has access to a ministry
$hasAccess = $user->ministries()->where('ministry_id', 16)->exists();

// Check if user has access to a specific department
$hasAccess = $user->departments()->where('department_id', 198)->exists();

// Get user's assigned ministries with departments
$assignments = $user->userMinistries()
    ->with(['ministry', 'department'])
    ->where('is_active', true)
    ->get();
```

### Filter Data by User Access
```php
// Get projects user can access based on ministry assignment
$projects = Project::whereHas('department.ministry.users', function($query) use ($user) {
    $query->where('user_id', $user->id)
          ->where('is_active', true);
})->get();
```

## Migration Files
- **2025_07_03_053052_create_user_ministries_table.php** - Creates the user_ministries table

## Seeder Updates
- **UserSeeder.php** - Now includes UserMinistry records for admin and PD users
- **DatabaseSeeder.php** - Updated to include proper seeding order

## Benefits

1. **Scalable Access Control**: Easy to assign users to different organizational levels
2. **Audit Trail**: Track user assignments with timestamps
3. **Flexible Permissions**: Combine with Spatie roles for comprehensive access control
4. **Data Security**: Users only see data they're authorized to access
5. **Organizational Alignment**: Reflects real government structure

## Next Steps

1. **Controller Updates**: Update controllers to filter data based on user assignments
2. **Middleware**: Create middleware to check user ministry/department access
3. **UI Updates**: Show only relevant ministries/departments in forms based on user access
4. **Reporting**: Generate reports based on user's assigned organizational scope

The system now has a complete organizational hierarchy with proper user assignments!
