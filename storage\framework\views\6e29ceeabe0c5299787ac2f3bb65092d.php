<?php $__env->startSection('title', 'Project Details'); ?>

<?php $__env->startSection('content'); ?>
<body class="bg-[#ebf3f8]">
    <!-- Navigation -->
    <div class="flex justify-between items-center p-5">
        <h2 class="text-[24px] text-[#0D163A] font-semibold">Project Details</h2>
        <div class="flex gap-3">
            
            <a href="<?php echo e(route('psdp.projects.index')); ?>"
               class="bg-gray-200 text-gray-700 px-6 py-3 rounded-full font-medium hover:bg-gray-300">
                Back to Projects
            </a>
        </div>
    </div>

    <div class="p-5">
        <div class="bg-white w-full rounded-[14px]">

            <div class="text-center my-3 p-5">
                <h2 class="text-[#0D163A] text-[25px] font-semibold"><?php echo e($project->name); ?></h2>
                <p class="text-[16px]"><?php echo e($project->department->name ?? 'N/A'); ?></p>
            </div>

            <div class="w-full mx-auto bg-[#DBEEF2] py-3 flex gap-10 justify-between items-center">
                <div class="grid grid-cols-2 md:grid-cols-4 w-[80%] mx-auto">

                    <div class="flex items-center gap-3">
                        <img src="<?php echo e(asset('assets/approval-icon.svg')); ?>" class="w-[32px] h-[32px]">
                        <div class="font-medium">
                            <p class="text-md">Approval Forum</p>
                            <p class="text-lg font-bold -my-1"><?php echo e($project->approvingAuthority->name ?? 'N/A'); ?></p>
                            <p class="text-xs"><?php echo e($project->authority_approved_date ? $project->authority_approved_date->format('d-m-Y') : 'N/A'); ?></p>
                        </div>
                    </div>

                    <div class="flex items-center gap-3">
                        <img src="<?php echo e(asset('assets/start-date.svg')); ?>" class="w-[32px] h-[32px]">
                        <div class="font-medium">
                            <p class="text-sm">Start Date</p>
                            <p class="text-lg font-bold leading-6"><?php echo e($project->admin_approval_date ? $project->admin_approval_date->format('d-m-Y') : 'N/A'); ?></p>
                        </div>
                    </div>

                    <div class="flex items-center gap-3">
                        <img src="<?php echo e(asset('assets/end-date.svg')); ?>" class="w-[32px] h-[32px]">
                        <div class="font-medium">
                            <p class="text-sm">Completion date as per PC-I</p>
                            <p class="text-lg font-bold leading-6"><?php echo e($project->completion_date_pc1 ? $project->completion_date_pc1->format('d-m-Y') : 'N/A'); ?></p>
                        </div>
                    </div>

                    <div class="flex items-center gap-3">
                        <img src="<?php echo e(asset('assets/completion-date.svg')); ?>" class="w-[32px] h-[32px]">
                        <div class="font-medium">
                            <p class="text-sm">Likely Completion date</p>
                            <p class="text-lg font-bold leading-6"><?php echo e($project->likely_completion_date ? $project->likely_completion_date->format('d-m-Y') : 'N/A'); ?></p>
                        </div>
                    </div>

                </div>
            </div>

            <div class="py-12 px-8">
                <div class="flex items-center gap-10">
                    <?php
                        $totalBudget = $project->grand_amount ?? $project->total_cost ?? 0;
                        $allocated = $project->allocations->sum('allocation_amount') ?? 0;
                        $expenditure = $project->allocations->sum('expense_amount') ?? 0;
                        $cumulativeExp = $project->allocations->sum('release_amount') ?? 0;
                        $utilizationPercentage = $cumulativeExp > 0 ? ($expenditure / $cumulativeExp) * 100 : 0;
                    ?>

                    <div class="w-[35%] flex flex-col gap-5">

                        <div class="w-full border border-[#D6DCE4] rounded-[10px]">
                            <div class="flex items-center justify-between px-4 pt-4 pb-8 border-b border-[#B6B6B6]">
                                <div class="flex items-center gap-2">
                                    <img src="<?php echo e(asset('assets/coin-icon.svg')); ?>" alt="">
                                    <p class="text-[18px] font-semibold">Project Cost</p>
                                </div>
                                <div class="text-white text-[18px] font-semibold bg-[#1A92AA] px-6 py-2 rounded-full cursor-pointer">
                                    Rs. <?php echo e(number_format($totalBudget, 2)); ?> M
                                </div>
                            </div>

                            <div class="flex px-4 justify-between">
                                <div class="flex flex-col justify-center gap-3">
                                    <p class="text-[12px] text-[#0FAE96] font-500">Utilization</p>
                                    <p class="text-[18px] font-bold"><?php echo e(number_format($utilizationPercentage, 0)); ?>%</p>
                                </div>
                                <div id="chart4" class="w-[112px]"></div>
                            </div>
                        </div>

                        <div class="w-full border border-[#D6DCE4] rounded-[10px]">
                            <div class="p-5">
                                <p class="text-center text-[16px] mb-3">Current FY</p>

                                <div class="flex flex-col gap-2">
                                    <div class="flex flex-col gap-3">
                                        <div class="flex items-center justify-center gap-3">
                                            <p class="mr-2 w-25 text-right">Allocation</p>
                                            <div class="px-12 text-white py-2 bg-[#F29797] rounded-full">Rs. <?php echo e(number_format($allocated, 2)); ?> M</div>
                                        </div>
                                    </div>

                                    <div class="flex flex-col gap-3">
                                        <div class="flex items-center justify-center gap-3">
                                            <p class="mr-2 w-25 text-right">Releases</p>
                                            <div class="px-12 text-white py-2 bg-[#FFD388] rounded-full">Rs. <?php echo e(number_format($cumulativeExp, 2)); ?> M</div>
                                        </div>
                                    </div>

                                    <div class="flex flex-col gap-3">
                                        <div class="flex items-center justify-center gap-3">
                                            <p class="mr-2 w-25 text-right">Expenditure</p>
                                            <div class="px-12 text-white py-2 bg-[#A7A0F1] rounded-full">Rs. <?php echo e(number_format($expenditure, 2)); ?> M</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="w-[65%]">
                        <div class="w-full border border-[#D6DCE4] p-4 rounded-[10px]">
                            <p class="text-[18px] font-semibold">Current Status</p>
                            <div id="chart5"></div>
                        </div>
                    </div>

                </div>
            </div>

            <div class="px-8 py-12">
                <p class="text-center font-semibold text-[25px] py-4">Overall Progress</p>

                <div class="w-full flex gap-10 mt-5">
                    <div class="p-10 w-[50%] border border-[#D6DCE4] rounded-[10px] flex gap-4 flex-col justify-center items-center">
                        <p class="text-[25px] font-semibold">Total Releases for Project</p>
                        <div class="px-12 py-3 font-semibold bg-[#A8E0F1] rounded-full">
                            Total Rs. <?php echo e(number_format($cumulativeExp, 3)); ?> M
                        </div>
                    </div>

                    <div class="p-10 w-[50%] border border-[#D6DCE4] rounded-[10px] flex gap-4 flex-col justify-center items-center">
                        <p class="text-[25px] font-semibold">Total Expenditure for Project</p>
                        <div class="px-12 py-3 font-semibold bg-[#F8C8C0] rounded-full">
                            Total Rs. <?php echo e(number_format($expenditure, 3)); ?> M
                        </div>
                    </div>
                </div>

                <div class="w-full flex gap-10 mt-5">
                    <div class="w-[50%] p-10 flex flex-col justify-center items-center border border-[#D6DCE4] rounded-[10px]">
                        <div id="radialChart"></div>
                        <p class="text-[25px] font-bold">Physical Allocation</p>
                    </div>

                    <div class="w-[50%] p-10 flex flex-col justify-center items-center border border-[#D6DCE4] rounded-[10px]">
                        <div id="radialChart2"></div>
                        <p class="text-[25px] font-bold">Financial Progress</p>
                    </div>
                </div>
            </div>

        </div>
    </div>
</body>

<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    <?php
        $physicalProgress = 80; // You can calculate this based on your business logic
        $financialProgress = $utilizationPercentage;
    ?>

    // Small utilization trend chart (chart4)
    <?php
        // Generate trend data based on utilization (you can replace this with actual historical data)
        $trendData = [
            max(0, $utilizationPercentage - 20),
            max(0, $utilizationPercentage - 10),
            max(0, $utilizationPercentage - 5),
            $utilizationPercentage
        ];
    ?>

    var chart4Options = {
        series: [{
            name: 'Utilization Trend',
            data: [<?php echo e(implode(',', $trendData)); ?>]
        }],
        chart: {
            type: 'area',
            height: 80,
            toolbar: {
                show: false
            },
            zoom: {
                enabled: false
            }
        },
        stroke: {
            curve: 'smooth',
            width: 3,
            colors: ['#14B8A6']
        },
        fill: {
            type: 'gradient',
            gradient: {
                shade: 'light',
                type: 'vertical',
                shadeIntensity: 0.5,
                gradientToColors: ['transparent'],
                inverseColors: false,
                opacityFrom: 0.4,
                opacityTo: 0,
                stops: [0, 100]
            }
        },
        colors: ['#14B8A6'],
        grid: {
            show: false
        },
        xaxis: {
            labels: {
                show: false
            },
            axisBorder: {
                show: false
            },
            axisTicks: {
                show: false
            },
            categories: [1, 2, 3, 4]
        },
        yaxis: {
            labels: {
                show: false
            }
        },
        tooltip: {
            enabled: false
        },
        markers: {
            size: 0
        },
        dataLabels: {
            enabled: false
        }
    };
    var chart4 = new ApexCharts(document.querySelector("#chart4"), chart4Options);
    chart4.render();

    // Current Status Chart (chart5)
    var chart5Options = {
        series: [{
            name: 'Allocation',
            data: [<?php echo e($allocated); ?>]
        }, {
            name: 'Financial Allocation',
            data: [<?php echo e($cumulativeExp); ?>]
        }, {
            name: 'Releases',
            data: [<?php echo e($expenditure); ?>]
        }, {
            name: 'Surrender',
            data: [<?php echo e($allocated - $cumulativeExp); ?>]
        }, {
            name: 'Expenditure',
            data: [<?php echo e($expenditure); ?>]
        }],
        chart: {
            type: 'bar',
            height: 350
        },
        plotOptions: {
            bar: {
                horizontal: false,
                columnWidth: '55%',
                endingShape: 'rounded'
            },
        },
        dataLabels: {
            enabled: false
        },
        stroke: {
            show: true,
            width: 2,
            colors: ['transparent']
        },
        xaxis: {
            categories: ['Current FY'],
        },
        yaxis: {
            title: {
                text: 'Amount (M)'
            }
        },
        fill: {
            opacity: 1
        },
        colors: ['#8B5CF6', '#10B981', '#F59E0B', '#6366F1', '#EF4444'],
        tooltip: {
            y: {
                formatter: function (val) {
                    return "Rs. " + val + " M"
                }
            }
        }
    };
    var chart5 = new ApexCharts(document.querySelector("#chart5"), chart5Options);
    chart5.render();

    // Physical Allocation Radial Chart
    var radialOptions = {
        series: [<?php echo e($physicalProgress); ?>],
        chart: {
            height: 250,
            type: 'radialBar',
        },
        plotOptions: {
            radialBar: {
                startAngle: -90,
                endAngle: 270,
                hollow: {
                    margin: 15,
                    size: '65%',
                },
                dataLabels: {
                    name: {
                        show: false
                    },
                    value: {
                        formatter: function(val) {
                            return parseInt(val) + '%';
                        },
                        color: '#1f2937',
                        fontSize: '30px',
                        fontWeight: 'bold',
                        show: true,
                        offsetY: 10
                    }
                }
            }
        },
        fill: {
            type: 'gradient',
            gradient: {
                shade: 'light',
                type: 'horizontal',
                shadeIntensity: 0.5,
                gradientToColors: ['#EE201C'],
                inverseColors: false,
                opacityFrom: 1,
                opacityTo: 1,
                stops: [0, 100],
                colorStops: [
                    {
                        offset: 0,
                        color: '#FCD34D',
                        opacity: 1
                    },
                    {
                        offset: 50,
                        color: '#F59E0B',
                        opacity: 1
                    },
                    {
                        offset: 100,
                        color: '#D97706',
                        opacity: 1
                    }
                ]
            }
        },
        stroke: {
            lineCap: 'round'
        },
    };
    var radialChart = new ApexCharts(document.querySelector("#radialChart"), radialOptions);
    radialChart.render();

    // Financial Progress Radial Chart
    var radialOptions2 = {
        series: [<?php echo e(number_format($financialProgress, 1)); ?>],
        chart: {
            height: 250,
            type: 'radialBar',
        },
        plotOptions: {
            radialBar: {
                startAngle: -90,
                endAngle: 270,
                hollow: {
                    margin: 15,
                    size: '65%',
                },
                dataLabels: {
                    name: {
                        show: false
                    },
                    value: {
                        formatter: function(val) {
                            return parseInt(val) + '%';
                        },
                        color: '#1f2937',
                        fontSize: '30px',
                        fontWeight: 'bold',
                        show: true,
                        offsetY: 10
                    }
                }
            }
        },
        fill: {
            type: 'gradient',
            gradient: {
                shade: 'light',
                type: 'horizontal',
                shadeIntensity: 0.5,
                gradientToColors: ['#DC2626'],
                inverseColors: false,
                opacityFrom: 1,
                opacityTo: 1,
                stops: [0, 100],
                colorStops: [
                    {
                        offset: 0,
                        color: '#FCA5A5',
                        opacity: 1
                    },
                    {
                        offset: 50,
                        color: '#F87171',
                        opacity: 1
                    },
                    {
                        offset: 100,
                        color: '#EF4444',
                        opacity: 1
                    }
                ]
            }
        },
        stroke: {
            lineCap: 'round'
        },
    };
    var radialChart2 = new ApexCharts(document.querySelector("#radialChart2"), radialOptions2);
    radialChart2.render();
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\psdb-dashboard-backend\resources\views/psdp/projects/show.blade.php ENDPATH**/ ?>