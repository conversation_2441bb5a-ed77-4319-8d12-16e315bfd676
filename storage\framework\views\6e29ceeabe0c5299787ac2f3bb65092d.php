<?php $__env->startSection('title', 'Project Details'); ?>

<?php $__env->startSection('content'); ?>
<body class="bg-[#ebf3f8]">
    <!-- Navigation -->
    <div class="flex justify-between items-center p-5">
        <h2 class="text-[24px] text-[#0D163A] font-semibold">Project Details</h2>
        <div class="flex gap-3">
            
            <a href="<?php echo e(route('psdp.projects.index')); ?>"
               class="bg-gray-200 text-gray-700 px-6 py-3 rounded-full font-medium hover:bg-gray-300">
                Back to Projects
            </a>
        </div>
    </div>

    <div class="p-5">
        <div class="bg-white w-full rounded-[14px]">

            <div class="text-center my-3 p-5">
                <h2 class="text-[#0D163A] text-[25px] font-semibold"><?php echo e($project->name); ?></h2>
                <p class="text-[16px]"><?php echo e($project->department->name ?? 'N/A'); ?></p>
            </div>

            <div class="w-full mx-auto bg-[#DBEEF2] py-3 flex gap-10 justify-between items-center">
                <div class="grid grid-cols-2 md:grid-cols-4 w-[80%] mx-auto">

                    <div class="flex items-center gap-3">
                        <img src="<?php echo e(asset('assets/approval-icon.svg')); ?>" class="w-[32px] h-[32px]">
                        <div class="font-medium">
                            <p class="text-md">Approval Forum</p>
                            <p class="text-lg font-bold -my-1"><?php echo e($project->approvingAuthority->name ?? 'N/A'); ?></p>
                            <p class="text-xs"><?php echo e($project->authority_approved_date ? $project->authority_approved_date->format('d-m-Y') : 'N/A'); ?></p>
                        </div>
                    </div>

                    <div class="flex items-center gap-3">
                        <img src="<?php echo e(asset('assets/start-date.svg')); ?>" class="w-[32px] h-[32px]">
                        <div class="font-medium">
                            <p class="text-sm">Start Date</p>
                            <p class="text-lg font-bold leading-6"><?php echo e($project->admin_approval_date ? $project->admin_approval_date->format('d-m-Y') : 'N/A'); ?></p>
                        </div>
                    </div>

                    <div class="flex items-center gap-3">
                        <img src="<?php echo e(asset('assets/end-date.svg')); ?>" class="w-[32px] h-[32px]">
                        <div class="font-medium">
                            <p class="text-sm">Completion date as per PC-I</p>
                            <p class="text-lg font-bold leading-6"><?php echo e($project->completion_date_pc1 ? $project->completion_date_pc1->format('d-m-Y') : 'N/A'); ?></p>
                        </div>
                    </div>

                    <div class="flex items-center gap-3">
                        <img src="<?php echo e(asset('assets/completion-date.svg')); ?>" class="w-[32px] h-[32px]">
                        <div class="font-medium">
                            <p class="text-sm">Likely Completion date</p>
                            <p class="text-lg font-bold leading-6"><?php echo e($project->likely_completion_date ? $project->likely_completion_date->format('d-m-Y') : 'N/A'); ?></p>
                        </div>
                    </div>

                </div>
            </div>

            <div class="py-12 px-8">
                <div class="flex items-center gap-10">
                    <?php
                        $totalBudget = $project->grand_amount ?? $project->total_cost ?? 0;
                        $allocated = $project->allocations->sum('allocation_amount') ?? 0;
                        $expenditure = $project->allocations->sum('expense_amount') ?? 0;
                        $cumulativeExp = $project->allocations->sum('release_amount') ?? 0;
                        $utilizationPercentage = $cumulativeExp > 0 ? ($expenditure / $cumulativeExp) * 100 : 0;
                    ?>

                    <div class="w-[35%] flex flex-col gap-5">

                        <div class="w-full border border-[#D6DCE4] rounded-[10px]">
                            <div class="flex items-center justify-between px-4 pt-4 pb-8 border-b border-[#B6B6B6]">
                                <div class="flex items-center gap-2">
                                    <img src="<?php echo e(asset('assets/coin-icon.svg')); ?>" alt="">
                                    <p class="text-[18px] font-semibold">Project Cost</p>
                                </div>
                                <div class="text-white text-[18px] font-semibold bg-[#1A92AA] px-6 py-2 rounded-full cursor-pointer">
                                    Rs. <?php echo e(number_format($totalBudget, 2)); ?> M
                                </div>
                            </div>

                            <div class="flex px-4 justify-between">
                                <div class="flex flex-col justify-center gap-3">
                                    <p class="text-[12px] text-[#0FAE96] font-500">Utilization</p>
                                    <p class="text-[18px] font-bold"><?php echo e(number_format($utilizationPercentage, 0)); ?>%</p>
                                </div>
                                <div id="chart4" class="w-[112px]"></div>
                            </div>
                        </div>

                        <div class="w-full border border-[#D6DCE4] rounded-[10px]">
                            <div class="p-5">
                                <p class="text-center text-[16px] mb-3">Current FY</p>

                                <div class="flex flex-col gap-2">
                                    <div class="flex flex-col gap-3">
                                        <div class="flex items-center justify-center gap-3">
                                            <p class="mr-2 w-25 text-right">Allocation</p>
                                            <div class="px-12 text-white py-2 bg-[#F29797] rounded-full">Rs. <?php echo e(number_format($allocated, 2)); ?> M</div>
                                        </div>
                                    </div>

                                    <div class="flex flex-col gap-3">
                                        <div class="flex items-center justify-center gap-3">
                                            <p class="mr-2 w-25 text-right">Releases</p>
                                            <div class="px-12 text-white py-2 bg-[#FFD388] rounded-full">Rs. <?php echo e(number_format($cumulativeExp, 2)); ?> M</div>
                                        </div>
                                    </div>

                                    <div class="flex flex-col gap-3">
                                        <div class="flex items-center justify-center gap-3">
                                            <p class="mr-2 w-25 text-right">Expenditure</p>
                                            <div class="px-12 text-white py-2 bg-[#A7A0F1] rounded-full">Rs. <?php echo e(number_format($expenditure, 2)); ?> M</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="w-[65%]">
                        <div class="w-full border border-[#D6DCE4] p-4 rounded-[10px]">
                            <p class="text-[18px] font-semibold">Current Status</p>
                            <div id="chart5"></div>
                        </div>
                    </div>

                </div>
            </div>

            <div class="px-8 py-12">
                <p class="text-center font-semibold text-[25px] py-4">Overall Progress</p>

                <div class="w-full flex gap-10 mt-5">
                    <div class="p-10 w-[50%] border border-[#D6DCE4] rounded-[10px] flex gap-4 flex-col justify-center items-center">
                        <p class="text-[25px] font-semibold">Total Releases for Project</p>
                        <div class="px-12 py-3 font-semibold bg-[#A8E0F1] rounded-full">
                            Total Rs. <?php echo e(number_format($cumulativeExp, 3)); ?> M
                        </div>
                    </div>

                    <div class="p-10 w-[50%] border border-[#D6DCE4] rounded-[10px] flex gap-4 flex-col justify-center items-center">
                        <p class="text-[25px] font-semibold">Total Expenditure for Project</p>
                        <div class="px-12 py-3 font-semibold bg-[#F8C8C0] rounded-full">
                            Total Rs. <?php echo e(number_format($expenditure, 3)); ?> M
                        </div>
                    </div>
                </div>

                <div class="w-full flex gap-10 mt-5">
                    <div class="w-[50%] p-10 flex flex-col justify-center items-center border border-[#D6DCE4] rounded-[10px]">
                        <div id="radialChart"></div>
                        <p class="text-[25px] font-bold">Physical Allocation</p>
                    </div>

                    <div class="w-[50%] p-10 flex flex-col justify-center items-center border border-[#D6DCE4] rounded-[10px]">
                        <div id="radialChart2"></div>
                        <p class="text-[25px] font-bold">Financial Progress</p>
                    </div>
                </div>
            </div>

            <!-- Deliverables & Tasks Progress Stats -->
            <div class="px-8 py-12 border-t border-gray-200">
                <p class="text-center font-semibold text-[25px] py-4">Deliverables & Tasks Progress</p>

                <?php
                    $totalDeliverables = $project->deliverables->count();
                    $totalTasks = $project->tasks->count();
                    $completedTasks = $project->tasks->where('actual_complete', 100)->count();
                    $inProgressTasks = $project->tasks->where('actual_complete', '>', 0)->where('actual_complete', '<', 100)->count();
                    $notStartedTasks = $project->tasks->where('actual_complete', 0)->count();

                    // Calculate average progress
                    $averageProgress = $totalTasks > 0 ? $project->tasks->avg('actual_complete') : 0;

                    // Calculate task completion percentage
                    $taskCompletionPercentage = $totalTasks > 0 ? ($completedTasks / $totalTasks) * 100 : 0;

                    // Get status distribution
                    $statusCounts = $project->tasks->groupBy('status.name')->map->count();
                    $onTrackTasks = $statusCounts->get('On-track', 0);
                    $delayedTasks = $statusCounts->get('Delayed', 0);
                    $completedStatusTasks = $statusCounts->get('Completed', 0);
                ?>

                <!-- Summary Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-6 rounded-xl shadow-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-blue-100 text-sm">Total Deliverables</p>
                                <p class="text-3xl font-bold"><?php echo e($totalDeliverables); ?></p>
                            </div>
                            <div class="bg-blue-400 p-3 rounded-full">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gradient-to-r from-green-500 to-green-600 text-white p-6 rounded-xl shadow-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-green-100 text-sm">Total Tasks</p>
                                <p class="text-3xl font-bold"><?php echo e($totalTasks); ?></p>
                            </div>
                            <div class="bg-green-400 p-3 rounded-full">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 5H7a2 2 0 00-2 2v6a2 2 0 002 2h6a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gradient-to-r from-purple-500 to-purple-600 text-white p-6 rounded-xl shadow-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-purple-100 text-sm">Completed Tasks</p>
                                <p class="text-3xl font-bold"><?php echo e($completedTasks); ?></p>
                            </div>
                            <div class="bg-purple-400 p-3 rounded-full">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gradient-to-r from-orange-500 to-orange-600 text-white p-6 rounded-xl shadow-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-orange-100 text-sm">Average Progress</p>
                                <p class="text-3xl font-bold"><?php echo e(number_format($averageProgress, 1)); ?>%</p>
                            </div>
                            <div class="bg-orange-400 p-3 rounded-full">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Progress Charts and Details -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
                    <!-- Task Status Distribution Chart -->
                    <div class="bg-white border border-[#D6DCE4] rounded-xl p-6 shadow-sm">
                        <h3 class="text-xl font-semibold text-gray-800 mb-6">Task Status Distribution</h3>
                        <div id="taskStatusChart" class="h-64"></div>
                        <div class="mt-4 space-y-2">
                            <div class="flex items-center justify-between text-sm">
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                    <span>Completed</span>
                                </div>
                                <span class="font-semibold"><?php echo e($completedStatusTasks); ?></span>
                            </div>
                            <div class="flex items-center justify-between text-sm">
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                                    <span>On-track</span>
                                </div>
                                <span class="font-semibold"><?php echo e($onTrackTasks); ?></span>
                            </div>
                            <div class="flex items-center justify-between text-sm">
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                                    <span>Delayed</span>
                                </div>
                                <span class="font-semibold"><?php echo e($delayedTasks); ?></span>
                            </div>
                        </div>
                    </div>

                    <!-- Task Progress Chart -->
                    <div class="bg-white border border-[#D6DCE4] rounded-xl p-6 shadow-sm">
                        <h3 class="text-xl font-semibold text-gray-800 mb-6">Task Progress Overview</h3>
                        <div id="taskProgressChart" class="h-64"></div>
                        <div class="mt-4 grid grid-cols-3 gap-4 text-center">
                            <div class="p-3 bg-gray-50 rounded-lg">
                                <div class="text-xl font-bold text-gray-600"><?php echo e($notStartedTasks); ?></div>
                                <div class="text-xs text-gray-500">Not Started</div>
                            </div>
                            <div class="p-3 bg-yellow-50 rounded-lg">
                                <div class="text-xl font-bold text-yellow-600"><?php echo e($inProgressTasks); ?></div>
                                <div class="text-xs text-gray-500">In Progress</div>
                            </div>
                            <div class="p-3 bg-green-50 rounded-lg">
                                <div class="text-xl font-bold text-green-600"><?php echo e($completedTasks); ?></div>
                                <div class="text-xs text-gray-500">Completed</div>
                            </div>
                        </div>
                    </div>

                    <!-- Deliverable Progress Chart -->
                    <div class="bg-white border border-[#D6DCE4] rounded-xl p-6 shadow-sm">
                        <h3 class="text-xl font-semibold text-gray-800 mb-6">Deliverable Progress</h3>
                        <div id="deliverableProgressChart" class="h-64"></div>
                        <div class="mt-4">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm font-medium text-gray-700">Overall Completion</span>
                                <span class="text-sm font-bold text-gray-900"><?php echo e(number_format($averageProgress, 1)); ?>%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-3">
                                <div class="bg-gradient-to-r from-blue-400 to-blue-600 h-3 rounded-full transition-all duration-300"
                                     style="width: <?php echo e($averageProgress); ?>%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Deliverables Timeline -->
                <div class="bg-white border border-[#D6DCE4] rounded-xl p-6 shadow-sm mb-8">
                    <h3 class="text-xl font-semibold text-gray-800 mb-6">Deliverables & Tasks Timeline</h3>
                    <div class="space-y-8">
                        <?php $__currentLoopData = $project->deliverables; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $deliverable): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
                            $deliverableTasks = $deliverable->tasks;
                            $deliverableProgress = $deliverableTasks->count() > 0 ? $deliverableTasks->avg('actual_complete') : 0;
                            $completedTasksCount = $deliverableTasks->where('actual_complete', 100)->count();
                        ?>
                        <div class="relative">
                            <!-- Deliverable Header -->
                            <div class="flex items-start space-x-4 mb-4">
                                <div class="flex-shrink-0 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                                    <?php echo e($loop->iteration); ?>

                                </div>
                                <div class="flex-grow">
                                    <div class="flex items-center justify-between mb-2">
                                        <h4 class="text-lg font-semibold text-gray-800"><?php echo e($deliverable->name); ?></h4>
                                        <div class="flex items-center space-x-4">
                                            <span class="text-sm text-gray-600"><?php echo e($completedTasksCount); ?>/<?php echo e($deliverableTasks->count()); ?> tasks completed</span>
                                            <div class="w-24 bg-gray-200 rounded-full h-2">
                                                <div class="bg-blue-500 h-2 rounded-full" style="width: <?php echo e($deliverableProgress); ?>%"></div>
                                            </div>
                                            <span class="text-sm font-semibold text-blue-600"><?php echo e(number_format($deliverableProgress, 1)); ?>%</span>
                                        </div>
                                    </div>

                                    <!-- Tasks Timeline -->
                                    <div class="ml-4 border-l-2 border-gray-200 pl-6 space-y-4">
                                        <?php $__currentLoopData = $deliverableTasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php
                                            $taskProgress = $task->actual_complete;
                                            $isCompleted = $taskProgress >= 100;
                                            $isDelayed = $task->status->name === 'Delayed';
                                            $isOnTrack = $task->status->name === 'On-track';

                                            $statusColor = $isCompleted ? 'green' : ($isDelayed ? 'red' : ($isOnTrack ? 'blue' : 'gray'));
                                            $bgColor = $isCompleted ? 'bg-green-50 border-green-200' : ($isDelayed ? 'bg-red-50 border-red-200' : ($isOnTrack ? 'bg-blue-50 border-blue-200' : 'bg-gray-50 border-gray-200'));
                                        ?>
                                        <div class="relative <?php echo e($bgColor); ?> border rounded-lg p-4">
                                            <!-- Timeline dot -->
                                            <div class="absolute -left-8 top-4 w-4 h-4 bg-<?php echo e($statusColor); ?>-500 rounded-full border-2 border-white shadow"></div>

                                            <div class="flex items-start justify-between">
                                                <div class="flex-grow">
                                                    <div class="flex items-center space-x-3 mb-2">
                                                        <span class="font-medium text-gray-800"><?php echo e($task->code); ?></span>
                                                        <span class="px-2 py-1 text-xs font-medium rounded-full
                                                            <?php if($isCompleted): ?> bg-green-100 text-green-800
                                                            <?php elseif($isDelayed): ?> bg-red-100 text-red-800
                                                            <?php elseif($isOnTrack): ?> bg-blue-100 text-blue-800
                                                            <?php else: ?> bg-gray-100 text-gray-800 <?php endif; ?>">
                                                            <?php echo e($task->status->name); ?>

                                                        </span>
                                                    </div>
                                                    <p class="text-gray-700 mb-3"><?php echo e($task->description); ?></p>

                                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                                        <div>
                                                            <span class="text-gray-500">Start Date:</span>
                                                            <span class="font-medium"><?php echo e($task->start_date ? $task->start_date->format('M d, Y') : 'Not set'); ?></span>
                                                        </div>
                                                        <div>
                                                            <span class="text-gray-500">End Date:</span>
                                                            <span class="font-medium"><?php echo e($task->end_date ? $task->end_date->format('M d, Y') : 'Not set'); ?></span>
                                                        </div>
                                                        <div>
                                                            <span class="text-gray-500">Duration:</span>
                                                            <span class="font-medium">
                                                                <?php if($task->start_date && $task->end_date): ?>
                                                                    <?php echo e($task->start_date->diffInDays($task->end_date)); ?> days
                                                                <?php else: ?>
                                                                    Not calculated
                                                                <?php endif; ?>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="flex-shrink-0 ml-4 text-right">
                                                    <div class="text-2xl font-bold text-<?php echo e($statusColor); ?>-600"><?php echo e(number_format($taskProgress, 1)); ?>%</div>
                                                    <div class="w-20 bg-gray-200 rounded-full h-2 mt-1">
                                                        <div class="bg-<?php echo e($statusColor); ?>-500 h-2 rounded-full" style="width: <?php echo e($taskProgress); ?>%"></div>
                                                    </div>
                                                    <div class="text-xs text-gray-500 mt-1">
                                                        Planned: <?php echo e(number_format($task->planned_complete, 1)); ?>%
                                                    </div>
                                                </div>
                                            </div>

                                            <?php if($task->remarks): ?>
                                            <div class="mt-3 p-3 bg-white rounded border-l-4 border-<?php echo e($statusColor); ?>-300">
                                                <p class="text-sm text-gray-600"><strong>Remarks:</strong> <?php echo e($task->remarks); ?></p>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>

        </div>
    </div>
</body>

<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    <?php
        $physicalProgress = 80; // You can calculate this based on your business logic
        $financialProgress = $utilizationPercentage;
    ?>

    // Small utilization trend chart (chart4)
    <?php
        // Generate trend data based on utilization (you can replace this with actual historical data)
        $trendData = [
            max(0, $utilizationPercentage - 20),
            max(0, $utilizationPercentage - 10),
            max(0, $utilizationPercentage - 5),
            $utilizationPercentage
        ];
    ?>

    var chart4Options = {
        series: [{
            name: 'Utilization Trend',
            data: [<?php echo e(implode(',', $trendData)); ?>]
        }],
        chart: {
            type: 'area',
            height: 80,
            toolbar: {
                show: false
            },
            zoom: {
                enabled: false
            }
        },
        stroke: {
            curve: 'smooth',
            width: 3,
            colors: ['#14B8A6']
        },
        fill: {
            type: 'gradient',
            gradient: {
                shade: 'light',
                type: 'vertical',
                shadeIntensity: 0.5,
                gradientToColors: ['transparent'],
                inverseColors: false,
                opacityFrom: 0.4,
                opacityTo: 0,
                stops: [0, 100]
            }
        },
        colors: ['#14B8A6'],
        grid: {
            show: false
        },
        xaxis: {
            labels: {
                show: false
            },
            axisBorder: {
                show: false
            },
            axisTicks: {
                show: false
            },
            categories: [1, 2, 3, 4]
        },
        yaxis: {
            labels: {
                show: false
            }
        },
        tooltip: {
            enabled: false
        },
        markers: {
            size: 0
        },
        dataLabels: {
            enabled: false
        }
    };
    var chart4 = new ApexCharts(document.querySelector("#chart4"), chart4Options);
    chart4.render();

    // Current Status Chart (chart5)
    var chart5Options = {
        series: [{
            name: 'Allocation',
            data: [<?php echo e($allocated); ?>]
        }, {
            name: 'Financial Allocation',
            data: [<?php echo e($cumulativeExp); ?>]
        }, {
            name: 'Releases',
            data: [<?php echo e($expenditure); ?>]
        }, {
            name: 'Surrender',
            data: [<?php echo e($allocated - $cumulativeExp); ?>]
        }, {
            name: 'Expenditure',
            data: [<?php echo e($expenditure); ?>]
        }],
        chart: {
            type: 'bar',
            height: 350
        },
        plotOptions: {
            bar: {
                horizontal: false,
                columnWidth: '55%',
                endingShape: 'rounded'
            },
        },
        dataLabels: {
            enabled: false
        },
        stroke: {
            show: true,
            width: 2,
            colors: ['transparent']
        },
        xaxis: {
            categories: ['Current FY'],
        },
        yaxis: {
            title: {
                text: 'Amount (M)'
            }
        },
        fill: {
            opacity: 1
        },
        colors: ['#8B5CF6', '#10B981', '#F59E0B', '#6366F1', '#EF4444'],
        tooltip: {
            y: {
                formatter: function (val) {
                    return "Rs. " + val + " M"
                }
            }
        }
    };
    var chart5 = new ApexCharts(document.querySelector("#chart5"), chart5Options);
    chart5.render();

    // Physical Allocation Radial Chart
    var radialOptions = {
        series: [<?php echo e($physicalProgress); ?>],
        chart: {
            height: 250,
            type: 'radialBar',
        },
        plotOptions: {
            radialBar: {
                startAngle: -90,
                endAngle: 270,
                hollow: {
                    margin: 15,
                    size: '65%',
                },
                dataLabels: {
                    name: {
                        show: false
                    },
                    value: {
                        formatter: function(val) {
                            return parseInt(val) + '%';
                        },
                        color: '#1f2937',
                        fontSize: '30px',
                        fontWeight: 'bold',
                        show: true,
                        offsetY: 10
                    }
                }
            }
        },
        fill: {
            type: 'gradient',
            gradient: {
                shade: 'light',
                type: 'horizontal',
                shadeIntensity: 0.5,
                gradientToColors: ['#EE201C'],
                inverseColors: false,
                opacityFrom: 1,
                opacityTo: 1,
                stops: [0, 100],
                colorStops: [
                    {
                        offset: 0,
                        color: '#FCD34D',
                        opacity: 1
                    },
                    {
                        offset: 50,
                        color: '#F59E0B',
                        opacity: 1
                    },
                    {
                        offset: 100,
                        color: '#D97706',
                        opacity: 1
                    }
                ]
            }
        },
        stroke: {
            lineCap: 'round'
        },
    };
    var radialChart = new ApexCharts(document.querySelector("#radialChart"), radialOptions);
    radialChart.render();

    // Financial Progress Radial Chart
    var radialOptions2 = {
        series: [<?php echo e(number_format($financialProgress, 1)); ?>],
        chart: {
            height: 250,
            type: 'radialBar',
        },
        plotOptions: {
            radialBar: {
                startAngle: -90,
                endAngle: 270,
                hollow: {
                    margin: 15,
                    size: '65%',
                },
                dataLabels: {
                    name: {
                        show: false
                    },
                    value: {
                        formatter: function(val) {
                            return parseInt(val) + '%';
                        },
                        color: '#1f2937',
                        fontSize: '30px',
                        fontWeight: 'bold',
                        show: true,
                        offsetY: 10
                    }
                }
            }
        },
        fill: {
            type: 'gradient',
            gradient: {
                shade: 'light',
                type: 'horizontal',
                shadeIntensity: 0.5,
                gradientToColors: ['#DC2626'],
                inverseColors: false,
                opacityFrom: 1,
                opacityTo: 1,
                stops: [0, 100],
                colorStops: [
                    {
                        offset: 0,
                        color: '#FCA5A5',
                        opacity: 1
                    },
                    {
                        offset: 50,
                        color: '#F87171',
                        opacity: 1
                    },
                    {
                        offset: 100,
                        color: '#EF4444',
                        opacity: 1
                    }
                ]
            }
        },
        stroke: {
            lineCap: 'round'
        },
    };
    var radialChart2 = new ApexCharts(document.querySelector("#radialChart2"), radialOptions2);
    radialChart2.render();

    // Task Status Distribution Donut Chart
    var taskStatusOptions = {
        series: [<?php echo e($completedStatusTasks); ?>, <?php echo e($onTrackTasks); ?>, <?php echo e($delayedTasks); ?>],
        chart: {
            type: 'donut',
            height: 250
        },
        labels: ['Completed', 'On-track', 'Delayed'],
        colors: ['#10B981', '#3B82F6', '#EF4444'],
        plotOptions: {
            pie: {
                donut: {
                    size: '60%',
                    labels: {
                        show: true,
                        total: {
                            show: true,
                            label: 'Total Tasks',
                            formatter: function (w) {
                                return <?php echo e($totalTasks); ?>

                            }
                        }
                    }
                }
            }
        },
        dataLabels: {
            enabled: true,
            formatter: function (val, opts) {
                return opts.w.config.series[opts.seriesIndex]
            }
        },
        legend: {
            show: false
        },
        responsive: [{
            breakpoint: 480,
            options: {
                chart: {
                    height: 200
                }
            }
        }]
    };
    var taskStatusChart = new ApexCharts(document.querySelector("#taskStatusChart"), taskStatusOptions);
    taskStatusChart.render();

    // Task Progress Bar Chart
    var taskProgressOptions = {
        series: [{
            name: 'Tasks',
            data: [<?php echo e($notStartedTasks); ?>, <?php echo e($inProgressTasks); ?>, <?php echo e($completedTasks); ?>]
        }],
        chart: {
            type: 'bar',
            height: 250,
            toolbar: {
                show: false
            }
        },
        plotOptions: {
            bar: {
                horizontal: false,
                columnWidth: '55%',
                borderRadius: 8,
                dataLabels: {
                    position: 'top'
                }
            }
        },
        dataLabels: {
            enabled: true,
            offsetY: -20,
            style: {
                fontSize: '12px',
                colors: ["#304758"]
            }
        },
        xaxis: {
            categories: ['Not Started', 'In Progress', 'Completed'],
            labels: {
                style: {
                    fontSize: '12px'
                }
            }
        },
        yaxis: {
            title: {
                text: 'Number of Tasks'
            }
        },
        colors: ['#6B7280', '#F59E0B', '#10B981'],
        grid: {
            borderColor: '#f1f5f9'
        }
    };
    var taskProgressChart = new ApexCharts(document.querySelector("#taskProgressChart"), taskProgressOptions);
    taskProgressChart.render();

    // Deliverable Progress Chart
    <?php
        $deliverableData = $project->deliverables->map(function($deliverable) {
            $progress = $deliverable->tasks->count() > 0 ? $deliverable->tasks->avg('actual_complete') : 0;
            return [
                'name' => strlen($deliverable->name) > 15 ? substr($deliverable->name, 0, 15) . '...' : $deliverable->name,
                'progress' => round($progress, 1)
            ];
        });
    ?>

    var deliverableProgressOptions = {
        series: [{
            name: 'Progress %',
            data: [
                <?php $__currentLoopData = $deliverableData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php echo e($item['progress']); ?>,
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            ]
        }],
        chart: {
            type: 'bar',
            height: 250,
            toolbar: {
                show: false
            }
        },
        plotOptions: {
            bar: {
                horizontal: true,
                borderRadius: 6,
                dataLabels: {
                    position: 'top'
                }
            }
        },
        dataLabels: {
            enabled: true,
            formatter: function (val) {
                return val + '%'
            },
            offsetX: 10,
            style: {
                fontSize: '12px',
                colors: ['#fff']
            }
        },
        xaxis: {
            categories: [
                <?php $__currentLoopData = $deliverableData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    '<?php echo e($item['name']); ?>',
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            ],
            max: 100,
            labels: {
                formatter: function (val) {
                    return val + '%'
                }
            }
        },
        colors: ['#3B82F6'],
        grid: {
            borderColor: '#f1f5f9'
        }
    };
    var deliverableProgressChart = new ApexCharts(document.querySelector("#deliverableProgressChart"), deliverableProgressOptions);
    deliverableProgressChart.render();
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\psdb-dashboard-backend\resources\views/psdp/projects/show.blade.php ENDPATH**/ ?>