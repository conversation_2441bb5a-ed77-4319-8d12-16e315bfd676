<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PSDP\DashboardController;
use App\Http\Controllers\PSDP\ProjectController;
use App\Http\Controllers\PSDP\PDProjectController;
use App\Http\Controllers\PSDP\RevisionController;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Auth;

Route::get('/', function () {
    if (Auth::check()) {
        return redirect()->route('psdp.dashboard');
    }
    return redirect()->route('login');
});

// Authentication Routes
Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
Route::post('/login', [AuthController::class, 'login'])->name('login.post');
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

// PSDP Routes (Protected)
Route::middleware('auth')->prefix('psdp')->name('psdp.')->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/pd-dashboard', [DashboardController::class, 'pdDashboard'])->name('pd-dashboard');
    Route::resource('projects', ProjectController::class);

    // PD Projects and their revisions
    Route::resource('pd-projects', PDProjectController::class);
    Route::prefix('pd-projects')->name('pd-projects.')->group(function () {
        Route::resource('{pd_project}/revisions', RevisionController::class)->except(['index']);
        Route::get('revisions', [RevisionController::class, 'index'])->name('revisions.index');
    });

    // API routes for cascading dropdowns
    Route::get('/api/divisions/{ministry}', [ProjectController::class, 'getDivisions'])->name('api.divisions');
    Route::get('/api/departments/{ministry}', [ProjectController::class, 'getDepartments'])->name('api.departments');
    Route::get('/api/departments/division/{division}', [ProjectController::class, 'getDepartmentsByDivision'])->name('api.departments.division');

    // PD API routes for cascading dropdowns
    Route::get('/api/pd/divisions/{ministry}', [PDProjectController::class, 'getDivisions'])->name('api.pd.divisions');
    Route::get('/api/pd/departments/{ministry}', [PDProjectController::class, 'getDepartments'])->name('api.pd.departments');
    Route::get('/api/pd/departments/division/{division}', [PDProjectController::class, 'getDepartmentsByDivision'])->name('api.pd.departments.division');
});

// User Management Routes (Super Admin and Ministry Admin only)
Route::middleware(['auth', 'role:super admin|admin'])->group(function () {
    Route::resource('users', UserController::class);
    Route::get('/users/departments/{ministry}', [UserController::class, 'getDepartments'])->name('users.departments');
});
