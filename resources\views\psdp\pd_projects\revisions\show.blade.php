@extends('layouts.app')

@section('title', 'Revision Details')

@section('content')
<div class="py-10 px-6">
    <div class="max-w-6xl mx-auto">
        <!-- Breadcrumb -->
        <div class="flex items-center gap-2 mb-6 text-sm text-gray-600">
            <a href="{{ route('psdp.pd-dashboard') }}" class="hover:text-blue-600">PSDP Projects</a>
            <span>></span>
            <a href="{{ route('psdp.pd-projects.index') }}" class="hover:text-blue-600">Projects</a>
            <span>></span>
            <a href="{{ route('psdp.pd-projects.show', $revision->project->id) }}" class="hover:text-blue-600">{{ $revision->project->name }}</a>
            <span>></span>
            <span class="text-gray-900">Revision Details</span>
        </div>

        <!-- Header -->
        <div class="flex justify-between items-start mb-8">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Revision Details</h1>
                <p class="text-gray-600">Revision Date: {{ $revision->revised_date->format('M d, Y') }}</p>
            </div>
            <div class="flex gap-3">
                <a href="{{ route('psdp.pd-projects.revisions.edit', [$revision->project->id, $revision->id]) }}"
                   class="bg-[#1A92AA] text-white px-6 py-3 rounded-full font-medium hover:brightness-110">
                    Edit Revision
                </a>
                <a href="{{ route('psdp.pd-projects.show', $revision->project->id) }}"
                   class="bg-gray-200 text-gray-700 px-6 py-3 rounded-full font-medium hover:bg-gray-300">
                    Back to Project
                </a>
            </div>
        </div>

        <!-- Project Information -->
        <div class="bg-white rounded-xl shadow p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-6">Project Information</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Project Name</label>
                    <p class="text-gray-900">{{ $revision->project->name }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">PSDP ID</label>
                    <p class="text-gray-900">{{ $revision->project->psdp_id }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Department</label>
                    <p class="text-gray-900">{{ $revision->project->department->name ?? 'N/A' }}</p>
                </div>
            </div>
        </div>

        <!-- Revision Details -->
        <div class="bg-white rounded-xl shadow p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-6">Revision Information</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Authority Approved Date</label>
                    <p class="text-gray-900">{{ $revision->authority_approved_date ? $revision->authority_approved_date->format('M d, Y') : 'N/A' }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Approving Authority</label>
                    <p class="text-gray-900">{{ $revision->approvingAuthority->name ?? 'N/A' }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Admin Approval Date</label>
                    <p class="text-gray-900">{{ $revision->admin_approval_date ? $revision->admin_approval_date->format('M d, Y') : 'N/A' }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Revised Date</label>
                    <p class="text-gray-900">{{ $revision->revised_date->format('M d, Y') }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Added By</label>
                    <p class="text-gray-900">{{ $revision->addedByUser->name ?? 'N/A' }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Created At</label>
                    <p class="text-gray-900">{{ $revision->created_at->format('M d, Y H:i') }}</p>
                </div>
            </div>
        </div>

        <!-- Financial Information -->
        <div class="bg-white rounded-xl shadow p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-6">Financial Details</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-blue-50 rounded-lg p-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">LC Amount</label>
                    <p class="text-xl font-semibold text-blue-600">{{ number_format(($revision->lc_amount ?? 0) / 1000000, 3) }} Million PKR</p>
                </div>
                <div class="bg-green-50 rounded-lg p-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">FC Amount</label>
                    <p class="text-xl font-semibold text-green-600">{{ number_format(($revision->fc_amount ?? 0) / 1000000, 3) }} Million USD</p>
                </div>
                <div class="bg-yellow-50 rounded-lg p-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Additional Cost</label>
                    <p class="text-xl font-semibold text-yellow-600">{{ number_format(($revision->additional_cost ?? 0) / 1000000, 3) }} Million PKR</p>
                </div>
                <div class="bg-purple-50 rounded-lg p-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Total Cost</label>
                    <p class="text-xl font-semibold text-purple-600">{{ number_format((($revision->lc_amount ?? 0) + ($revision->fc_amount ?? 0)) / 1000000, 3) }} Million PKR</p>
                </div>
            </div>
        </div>

        <!-- Cost Impact -->
        <div class="bg-white rounded-xl shadow p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-6">Cost Impact Analysis</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="bg-gray-100 rounded-lg p-4">
                        <h3 class="text-sm font-medium text-gray-700 mb-2">Original Project Cost</h3>
                        <p class="text-2xl font-bold text-gray-900">{{ number_format(($revision->project->total_cost ?? 0) / 1000000, 3) }} Million PKR</p>
                    </div>
                </div>
                <div class="text-center">
                    <div class="bg-red-100 rounded-lg p-4">
                        <h3 class="text-sm font-medium text-gray-700 mb-2">Additional Cost (This Revision)</h3>
                        <p class="text-2xl font-bold text-red-600">{{ number_format(($revision->additional_cost ?? 0) / 1000000, 3) }} Million PKR</p>
                    </div>
                </div>
                <div class="text-center">
                    <div class="bg-green-100 rounded-lg p-4">
                        <h3 class="text-sm font-medium text-gray-700 mb-2">Current Grand Total</h3>
                        <p class="text-2xl font-bold text-green-600">{{ number_format(($revision->project->grand_amount ?? $revision->project->total_cost) / 1000000, 3) }} Million PKR</p>
                    </div>
                </div>
            </div>

            @if($revision->additional_cost > 0)
            <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                    <p class="text-yellow-800">
                        <strong>Cost Increase:</strong> This revision increased the project cost by {{ number_format(($revision->additional_cost) / 1000000, 3) }} Million PKR
                        ({{ number_format(($revision->additional_cost / ($revision->project->total_cost ?? 1)) * 100, 2) }}% increase from original cost).
                    </p>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection
