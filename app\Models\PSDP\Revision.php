<?php

namespace App\Models\PSDP;

use Illuminate\Database\Eloquent\Model;
use App\Models\User;

class Revision extends Model
{
    protected $fillable = [
        'project_id',
        'authority_approved_date',
        'approving_authority_id',
        'admin_approval_date',
        'revised_date',
        'lc_amount',
        'fc_amount',
        'additional_cost',
        'added_by_user_id'
    ];

    protected $casts = [
        'authority_approved_date' => 'date',
        'admin_approval_date' => 'date',
        'revised_date' => 'date',
        'lc_amount' => 'decimal:2',
        'fc_amount' => 'decimal:2',
        'additional_cost' => 'decimal:2'
    ];

    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function approvingAuthority()
    {
        return $this->belongsTo(ApprovingAuthority::class);
    }

    public function addedByUser()
    {
        return $this->belongsTo(User::class, 'added_by_user_id');
    }
}
