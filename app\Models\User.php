<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Database\Eloquent\Relations\HasMany;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Get the user ministries for the user.
     */
    public function userMinistries(): HasMany
    {
        return $this->hasMany(\App\Models\PSDP\UserMinistry::class);
    }

    /**
     * Get the ministries assigned to the user.
     */
    public function ministries()
    {
        return $this->belongsToMany(\App\Models\PSDP\Ministry::class, 'user_ministries')
                    ->withPivot('department_id', 'is_active')
                    ->withTimestamps();
    }

    /**
     * Get the departments assigned to the user.
     */
    public function departments()
    {
        return $this->belongsToMany(\App\Models\PSDP\Department::class, 'user_ministries')
                    ->withPivot('ministry_id', 'is_active')
                    ->withTimestamps();
    }
}
