<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Project Form</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-[#eaf3f9] py-10 px-6">

  <div class="max-w-full mx-auto bg-white rounded-xl shadow p-5 sm:p-12 sm:px-16">
    <!-- Header -->
    <h2 class="text-xl sm:text-2xl font-semibold text-center text-gray-900 mb-8">
      Projects Physical Progress Monitoring Matrix
    </h2>

    <!-- Project Info -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
     
    <div class="relative mt-4 w-full mx-auto">
            <input
              type="text"
              class="block w-full text-sm text-gray-900 bg-transparent rounded-full border  py-3 pl-4 pr-4 outline-0"
            placeholder="Please write here..." 
              />
            <label
              class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4"
            >
              Project Name
              
            </label>
          </div>
       <div class="relative mt-4 w-full mx-auto">
            <input
              type="text"
              class="block w-full text-sm text-gray-900 bg-transparent rounded-full border  py-3 pl-4 pr-4 outline-0"
            placeholder="Please write here..." 
              />
            <label
              class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4"
            >
              Executing Agency
              
            </label>
          </div>
    </div>



    <!-- Deliverable Title -->
   <div class="flex flex-col sm:flex-row gap-4 items-start sm:items-center mb-4">
  <h3 class="text-md font-semibold text-gray-800 whitespace-nowrap mr-4">
    Output/Deliverable-1 :
  </h3>

  <div class="flex-grow hidden sm:flex h-px bg-[repeating-linear-gradient(to_right,_#ccc,_#ccc_10px,_transparent_10px,_transparent_20px)]"></div>
<button class=" text-white px-4 py-2 text-sm rounded-full font-medium"
        style="background-color: #1A92AA;">
  + Add Another
</button>

</div>

    <!-- Deliverable Details -->
    <div class="flex flex-col sm:flex-row gap-6 mb-6">
  <!-- 30% width input -->
  <div class="relative mt-4 w-full sm:w-[30%]">
    <input
      type="text"
      class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
      placeholder="1.1"
    />
    <label
      class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4"
    >
      Code
    </label>
  </div>

  <!-- 70% width input -->
  <div class="relative mt-4 w-full sm:w-[70%]">
    <input
      type="text"
      class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
      placeholder="Please write here..."
    />
    <label
      class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4"
    >
      Task Description
    </label>
  </div>
</div>


    <!-- Date Inputs -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
       <div class="relative mt-2 w-full mx-auto">
            <input
              type="date"
              class="block w-full text-sm text-gray-900 bg-transparent rounded-full border  py-3 pl-4 pr-4 outline-0"
            placeholder="Please write here..." 
              />
            <label
              class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4"
            >
              Start Date
              
            </label>
          </div>
      <div class="relative mt-2 w-full mx-auto">
            <input
              type="date"
              class="block w-full text-sm text-gray-900 bg-transparent rounded-full border  py-3 pl-4 pr-4 outline-0"
            placeholder="Please write here..." 
              />
            <label
              class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4"
            >
              End Date
              
            </label>
          </div>
    </div>

    <!-- Progress Inputs -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
     <div class="relative -mt-2 w-full mx-auto">
            <input
              type="text"
              class="block w-full text-sm text-gray-900 bg-transparent rounded-full border  py-3 pl-4 pr-4 outline-0"
            placeholder="Please select start date..." 
              />
            <label
              class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4"
            >
              Planned % Complete
              
            </label>
          </div>
  <!-- Custom dropdown arrow -->
  <div class="relative -mt-2 w-full mx-auto">
            <input
              type="text"
              class="block w-full text-sm text-gray-900 bg-transparent rounded-full border  py-3 pl-4 pr-4 outline-0"
            placeholder="Please select end date..." 
              />
            <label
              class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4"
            >
              Actual % complete
              
            </label>
          </div>
    </div>

    <!-- Status and Remarks -->
   <div class="flex flex-col sm:flex-row gap-6 mb-6">
  <!-- 30% width select -->
  <div class="relative -mt-2 w-full sm:w-[30%]">
    <select
      class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0 appearance-none"
    >
      <option disabled selected>Please select...</option>
      <option>Option 1</option>
      <option>Option 2</option>
      <option>Option 3</option>
    </select>
    <label
      class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 start-4 -translate-y-1/2 scale-75 -translate-y-6"
    >
      Status
    </label>
    <!-- Custom dropdown arrow -->
    <div class="pointer-events-none absolute inset-y-0 right-4 flex items-center text-gray-500">
      <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
        <path d="M19 9l-7 7-7-7" />
      </svg>
    </div>
  </div>

  <!-- 70% width input -->
  <div class="relative -mt-2 w-full sm:w-[70%]">
    <input
      type="text"
      class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
      placeholder="Please write here..."
    />
    <label
      class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4"
    >
      Remarks
    </label>
  </div>
</div>


    <hr class="w-full mt-4 border-0 h-px" 
     style="background: repeating-linear-gradient(to right, #ccc, #ccc 10px, transparent 10px, transparent 20px);" />


    <!-- Deliverable Title -->
   <div class="flex flex-col sm:flex-row gap-4 items-start sm:items-center mt-8 my-4">
  <h3 class="text-md font-semibold text-gray-800 whitespace-nowrap mr-4">
    Output/Deliverable-2 :
  </h3>

  <div class="flex-grow hidden sm:flex h-px bg-[repeating-linear-gradient(to_right,_#ccc,_#ccc_10px,_transparent_10px,_transparent_20px)]"></div>
<button class=" text-white px-4 py-2 text-sm rounded-full font-medium"
        style="background-color: #1A92AA;">
  + Add Another
</button>

</div>

    <!-- Deliverable Details -->
    <div class="flex flex-col sm:flex-row gap-6 mb-6">
  <!-- 30% width input -->
  <div class="relative mt-4 w-full sm:w-[30%]">
    <input
      type="text"
      class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
      placeholder="1.1"
    />
    <label
      class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4"
    >
      Code
    </label>
  </div>

  <!-- 70% width input -->
  <div class="relative mt-4 w-full sm:w-[70%]">
    <input
      type="text"
      class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
      placeholder="Please write here..."
    />
    <label
      class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4"
    >
      Task Description
    </label>
  </div>
</div>


    <!-- Date Inputs -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
       <div class="relative mt-2 w-full mx-auto">
            <input
              type="date"
              class="block w-full text-sm text-gray-900 bg-transparent rounded-full border  py-3 pl-4 pr-4 outline-0"
            placeholder="Please write here..." 
              />
            <label
              class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4"
            >
              Start Date
              
            </label>
          </div>
      <div class="relative mt-2 w-full mx-auto">
            <input
              type="date"
              class="block w-full text-sm text-gray-900 bg-transparent rounded-full border  py-3 pl-4 pr-4 outline-0"
            placeholder="Please write here..." 
              />
            <label
              class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4"
            >
              End Date
              
            </label>
          </div>
    </div>

    <!-- Progress Inputs -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
     <div class="relative -mt-2 w-full mx-auto">
            <input
              type="text"
              class="block w-full text-sm text-gray-900 bg-transparent rounded-full border  py-3 pl-4 pr-4 outline-0"
            placeholder="Please select start date..." 
              />
            <label
              class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4"
            >
              Planned % Complete
              
            </label>
          </div>
  <!-- Custom dropdown arrow -->
  <div class="relative -mt-2 w-full mx-auto">
            <input
              type="text"
              class="block w-full text-sm text-gray-900 bg-transparent rounded-full border  py-3 pl-4 pr-4 outline-0"
            placeholder="Please select end date..." 
              />
            <label
              class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4"
            >
              Actual % complete
              
            </label>
          </div>
    </div>

    <!-- Status and Remarks -->
   <div class="flex flex-col sm:flex-row gap-6 mb-6">
  <!-- 30% width select -->
  <div class="relative -mt-2 w-full sm:w-[30%]">
    <select
      class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0 appearance-none"
    >
      <option disabled selected>Please select...</option>
      <option>Option 1</option>
      <option>Option 2</option>
      <option>Option 3</option>
    </select>
    <label
      class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 start-4 -translate-y-1/2 scale-75 -translate-y-6"
    >
      Status
    </label>
    <!-- Custom dropdown arrow -->
    <div class="pointer-events-none absolute inset-y-0 right-4 flex items-center text-gray-500">
      <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
        <path d="M19 9l-7 7-7-7" />
      </svg>
    </div>
  </div>

  <!-- 70% width input -->
  <div class="relative -mt-2 w-full sm:w-[70%]">
    <input
      type="text"
      class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
      placeholder="Please write here..."
    />
    <label
      class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4"
    >
      Remarks
    </label>
  </div>
</div>


    <hr class="w-full mt-4 border-0 h-px" 
     style="background: repeating-linear-gradient(to right, #ccc, #ccc 10px, transparent 10px, transparent 20px);" />


     <!-- Wrapper to center the buttons -->
<div class="flex justify-center mt-8">
  <!-- Fixed-width container -->
 <div class="flex flex-col sm:flex-row justify-center gap-4 w-full">
    <button
    class="w-full sm:w-36 text-black text-sm font-medium rounded-full px-4 py-2.5 hover:brightness-105"
    style="background-color: #D6DCE4;">
    Cancel
  </button>
  <button
    class="w-full sm:w-36 text-white text-sm font-medium rounded-full px-4 py-2.5 hover:brightness-110"
    style="background-color: #1A92AA;">
    Submit
  </button>

</div>


  </div>
  

</body>
</html>
