<?php

namespace App\Http\Controllers\PSDP;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\PSDP\Project;
use App\Models\PSDP\Department;
use App\Models\PSDP\ApprovingAuthority;
use App\Models\PSDP\Status;
use App\Models\PSDP\FinancialYear;
use App\Models\PSDP\Ministry;
use App\Models\PSDP\Division;
use App\Models\UserMinistry;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use App\Models\PSDP\Allocation;
use App\Models\PSDP\Quarter;
use App\Models\PSDP\Deliverable;
use App\Models\PSDP\Task;

class PDProjectController extends Controller
{
    public function __construct()
    {
        // Middleware will be handled in routes or via middleware groups
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = auth()->user();
        $projectsQuery = Project::with(['department', 'approvingAuthority']);

        // Apply role-based filtering
        if ($user->hasRole('super admin')) {
            // Super admin sees all projects - no filtering needed
        } elseif ($user->hasRole('admin')) {
            // Ministry admin sees projects from their ministry and all its departments
            $userMinistry = $user->userMinistries()->first();
            if ($userMinistry) {
                $projectsQuery->where('projects.ministry_id', $userMinistry->ministry_id);
            }
        } elseif ($user->hasRole('PD')) {
            // PD sees only projects from their specific department
            $userMinistry = $user->userMinistries()->first();
            if ($userMinistry && $userMinistry->department_id) {
                $projectsQuery->where('projects.department_id', $userMinistry->department_id);
            }
        }

        $projects = $projectsQuery->paginate(10);

        return view('psdp.pd_projects.index', compact('projects'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $user = auth()->user();
        $userMinistry = UserMinistry::where('user_id', $user->id)->first();

        // Get ministries, departments based on user role
        $ministries = collect();
        $departments = collect();

        if ($user->hasRole('super admin')) {
            // Super admin can see all ministries and departments
            $ministries = Ministry::where(function($query) {
                $query->where('isActive', true)->orWhereNull('isActive');
            })->get();
            $departments = Department::where(function($query) {
                $query->where('isActive', true)->orWhereNull('isActive');
            })->get();
        } elseif ($user->hasRole('admin')) {
            // Ministry admin sees their ministry and its departments
            $userMinistries = UserMinistry::where('user_id', $user->id)->pluck('ministry_id');
            $ministries = Ministry::whereIn('id', $userMinistries)->where(function($query) {
                $query->where('isActive', true)->orWhereNull('isActive');
            })->get();
            $departments = Department::whereIn('ministry_id', $userMinistries)->where(function($query) {
                $query->where('isActive', true)->orWhereNull('isActive');
            })->get();
        } elseif ($user->hasRole('PD')) {
            // PD sees only their ministry and department
            if ($userMinistry) {
                $ministries = Ministry::where('id', $userMinistry->ministry_id)->where(function($query) {
                    $query->where('isActive', true)->orWhereNull('isActive');
                })->get();
                $departments = Department::where('ministry_id', $userMinistry->ministry_id)->where(function($query) {
                    $query->where('isActive', true)->orWhereNull('isActive');
                })->get();
            }
        }

        $approvingAuthorities = ApprovingAuthority::all();
        $financialYears = FinancialYear::where('is_active', true)->orderBy('name', 'desc')->get();
        $statuses = Status::all();

        return view('psdp.pd_projects.create', compact(
            'ministries',
            'departments',
            'approvingAuthorities',
            'financialYears',
            'statuses'
        ));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Debug: Log all request data (remove in production)
        \Log::info('PD Project Store Request Data:', $request->all());

        $validator = Validator::make($request->all(), [
            // Step 1: Project Information
            'name' => 'required|string|max:255',
            'department_id' => 'required|exists:departments,id',
            'psdp_no' => 'required|string|max:255',
            'psdp_id' => 'required|string|max:255',
            'approving_authority_id' => 'required|exists:approving_authorities,id',
            'authority_approved_date' => 'nullable|date',
            'admin_approval_date' => 'nullable|date',
            'completion_date_pc1' => 'nullable|date',
            'likely_completion_date' => 'nullable|date',
            'total_cost' => 'nullable|numeric|min:0',
            'lc_amount' => 'nullable|numeric|min:0',
            'fc_amount' => 'nullable|numeric|min:0',

            // Step 2: Budget Allocations
            'allocations' => 'nullable|array',
            'allocations.*.financial_year_id' => 'required|exists:financial_years,id',
            'allocations.*.allocation_amount' => 'required|numeric|min:0',
            'allocations.*.expense_amount' => 'nullable|numeric|min:0',
            'allocations.*.release_amount' => 'nullable|numeric|min:0',
            'allocations.*.quarters' => 'nullable|array',
            'allocations.*.quarters.*.release_amount' => 'nullable|numeric|min:0',
            'allocations.*.quarters.*.expense_amount' => 'nullable|numeric|min:0',

            // Step 3: Deliverables and Tasks
            'deliverables' => 'nullable|array',
            'deliverables.*.name' => 'required|string|max:255',
            'deliverables.*.tasks' => 'nullable|array',
            'deliverables.*.tasks.*.code' => 'required|string|max:50',
            'deliverables.*.tasks.*.description' => 'required|string',
            'deliverables.*.tasks.*.start_date' => 'required|date',
            'deliverables.*.tasks.*.end_date' => 'required|date|after_or_equal:deliverables.*.tasks.*.start_date',
            'deliverables.*.tasks.*.planned_complete' => 'required|numeric|min:0|max:100',
        ]);

        if ($validator->fails()) {
            \Log::error('PD Project Validation Failed:', $validator->errors()->toArray());

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::beginTransaction();

            // Get user's ministry for role-based assignment
            $user = auth()->user();
            $userMinistry = $user->userMinistries()->with(['ministry', 'department'])->first();

            $project = new Project();
            $project->name = $request->name;
            $project->department_id = $request->department_id;

            // Set ministry_id based on user role
            if ($user->hasRole('super admin')) {
                // Super admin can create projects for any ministry
                $project->ministry_id = $request->ministry_id;
            } else {
                // Other users use their assigned ministry
                $project->ministry_id = $userMinistry ? $userMinistry->ministry_id : $request->ministry_id;
            }
            $project->psdp_no = $request->psdp_no;
            $project->psdp_id = $request->psdp_id;
            $project->approving_authority_id = $request->approving_authority_id;
            $project->authority_approved_date = $request->authority_approved_date;
            $project->admin_approval_date = $request->admin_approval_date;
            $project->completion_date_pc1 = $request->completion_date_pc1;
            $project->likely_completion_date = $request->likely_completion_date;
            // Convert from millions to actual amounts
            $project->total_cost = $request->total_cost * 1000000;
            $project->lc_amount = $request->lc_amount * 1000000;
            $project->fc_amount = $request->fc_amount * 1000000;
            $project->grand_amount = $request->total_cost * 1000000;
            $project->last_revised_date = $request->completion_date_pc1;
            $project->added_by_user_id = auth()->id();
            $project->save();

            // Step 2: Save Budget Allocations
            if ($request->has('allocations') && is_array($request->allocations)) {
                foreach ($request->allocations as $allocationData) {
                    $allocation = new Allocation();
                    $allocation->project_id = $project->id;
                    $allocation->financial_year_id = $allocationData['financial_year_id'];
                    // Convert from millions to actual amounts
                    $allocation->allocation_amount = ($allocationData['allocation_amount'] ?? 0) * 1000000;
                    $allocation->expense_amount = ($allocationData['expense_amount'] ?? 0) * 1000000;
                    $allocation->release_amount = ($allocationData['release_amount'] ?? 0) * 1000000;
                    $allocation->save();

                    // Save quarterly data if provided
                    if (isset($allocationData['quarters']) && is_array($allocationData['quarters'])) {
                        foreach ($allocationData['quarters'] as $quarterIndex => $quarterData) {
                            if (!empty($quarterData['release_amount']) || !empty($quarterData['expense_amount'])) {
                                $quarter = new Quarter();
                                $quarter->project_id = $project->id;
                                $quarter->allocation_id = $allocation->id;
                                $quarter->quarter = 'Q' . ($quarterIndex + 1);
                                // Convert from millions to actual amounts
                                $quarter->release_amount = ($quarterData['release_amount'] ?? 0) * 1000000;
                                $quarter->expense_amount = ($quarterData['expense_amount'] ?? 0) * 1000000;
                                $quarter->save();
                            }
                        }
                    }
                }
            }

            // Step 3: Save Deliverables and Tasks
            if ($request->has('deliverables') && is_array($request->deliverables)) {
                foreach ($request->deliverables as $deliverableData) {
                    $deliverable = new Deliverable();
                    $deliverable->project_id = $project->id;
                    $deliverable->name = $deliverableData['name'];
                    $deliverable->save();

                    // Save tasks for this deliverable
                    if (isset($deliverableData['tasks']) && is_array($deliverableData['tasks'])) {
                        foreach ($deliverableData['tasks'] as $taskData) {
                            $task = new Task();
                            $task->project_id = $project->id;
                            $task->deliverable_id = $deliverable->id;
                            $task->code = $taskData['code'];
                            $task->description = $taskData['description'];
                            $task->start_date = $taskData['start_date'];
                            $task->end_date = $taskData['end_date'];
                            $task->planned_complete = $taskData['planned_complete'] ?? 0;
                            $task->actual_complete = 0; // Default to 0 for new tasks

                            // Get default status (first active status or create a default one)
                            $defaultStatus = \App\Models\PSDP\Status::where('is_active', true)->first();
                            $task->status_id = $defaultStatus ? $defaultStatus->id : 1;
                            $task->save();
                        }
                    }
                }
            }

            DB::commit();

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Project created successfully!',
                    'redirect' => route('psdp.pd-projects.index')
                ]);
            }

            return redirect()->route('psdp.pd-projects.index')
                ->with('success', 'Project created successfully.');

        } catch (\Exception $e) {
            DB::rollback();

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error creating project: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Error creating project: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Project $pd_project)
    {
        $pd_project->load(['department', 'approvingAuthority', 'allocations', 'revisions']);
        $project = $pd_project; // For compatibility with existing view
        return view('psdp.pd_projects.show', compact('pd_project', 'project'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Project $pd_project)
    {
        $user = auth()->user();
        $userMinistry = UserMinistry::where('user_id', $user->id)->first();

        // Get ministries, departments based on user role
        $ministries = collect();
        $departments = collect();

        if ($user->hasRole('super admin')) {
            $ministries = Ministry::where(function($query) {
                $query->where('isActive', true)->orWhereNull('isActive');
            })->get();
            $departments = Department::where(function($query) {
                $query->where('isActive', true)->orWhereNull('isActive');
            })->get();
        } elseif ($user->hasRole('admin')) {
            $userMinistries = UserMinistry::where('user_id', $user->id)->pluck('ministry_id');
            $ministries = Ministry::whereIn('id', $userMinistries)->where(function($query) {
                $query->where('isActive', true)->orWhereNull('isActive');
            })->get();
            $departments = Department::whereIn('ministry_id', $userMinistries)->where(function($query) {
                $query->where('isActive', true)->orWhereNull('isActive');
            })->get();
        } elseif ($user->hasRole('PD')) {
            if ($userMinistry) {
                $ministries = Ministry::where('id', $userMinistry->ministry_id)->where(function($query) {
                    $query->where('isActive', true)->orWhereNull('isActive');
                })->get();
                $departments = Department::where('ministry_id', $userMinistry->ministry_id)->where(function($query) {
                    $query->where('isActive', true)->orWhereNull('isActive');
                })->get();
            }
        }

        $approvingAuthorities = ApprovingAuthority::all();
        $financialYears = FinancialYear::where('is_active', true)->orderBy('name', 'desc')->get();
        $statuses = Status::all();
        $project = $pd_project; // For compatibility with existing view

        // Convert amounts from database values to millions for display
        $project->total_cost_millions = $project->total_cost / 1000000;
        $project->lc_amount_millions = $project->lc_amount / 1000000;
        $project->fc_amount_millions = $project->fc_amount / 1000000;
        $project->grand_amount_millions = $project->grand_amount / 1000000;

        // Convert allocation amounts to millions for display
        $project->load(['allocations.quarters']);
        foreach ($project->allocations as $allocation) {
            $allocation->allocation_amount_millions = $allocation->allocation_amount / 1000000;
            $allocation->expense_amount_millions = $allocation->expense_amount / 1000000;
            $allocation->release_amount_millions = $allocation->release_amount / 1000000;

            foreach ($allocation->quarters as $quarter) {
                $quarter->release_amount_millions = $quarter->release_amount / 1000000;
                $quarter->expense_amount_millions = $quarter->expense_amount / 1000000;
            }
        }

        return view('psdp.pd_projects.edit', compact(
            'pd_project',
            'project',
            'ministries',
            'departments',
            'approvingAuthorities',
            'financialYears',
            'statuses'
        ));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Project $pd_project)
    {
        // Debug: Log all request data (remove in production)
        \Log::info('PD Project Update Request Data:', $request->all());

        $validator = Validator::make($request->all(), [
            // Step 1: Project Information
            'name' => 'required|string|max:255',
            'department_id' => 'required|exists:departments,id',
            'psdp_no' => 'required|string|max:255',
            'psdp_id' => 'required|string|max:255',
            'approving_authority_id' => 'required|exists:approving_authorities,id',
            'authority_approved_date' => 'nullable|date',
            'admin_approval_date' => 'nullable|date',
            'completion_date_pc1' => 'nullable|date',
            'likely_completion_date' => 'nullable|date',
            'total_cost' => 'nullable|numeric|min:0',
            'lc_amount' => 'nullable|numeric|min:0',
            'fc_amount' => 'nullable|numeric|min:0',

            // Step 2: Budget Allocations
            'allocations' => 'nullable|array',
            'allocations.*.financial_year_id' => 'required|exists:financial_years,id',
            'allocations.*.allocation_amount' => 'required|numeric|min:0',
            'allocations.*.expense_amount' => 'nullable|numeric|min:0',
            'allocations.*.release_amount' => 'nullable|numeric|min:0',
            'allocations.*.quarters' => 'nullable|array',
            'allocations.*.quarters.*.release_amount' => 'nullable|numeric|min:0',
            'allocations.*.quarters.*.expense_amount' => 'nullable|numeric|min:0',

            // Step 3: Deliverables and Tasks
            'deliverables' => 'nullable|array',
            'deliverables.*.name' => 'required|string|max:255',
            'deliverables.*.tasks' => 'nullable|array',
            'deliverables.*.tasks.*.code' => 'required|string|max:50',
            'deliverables.*.tasks.*.description' => 'required|string',
            'deliverables.*.tasks.*.start_date' => 'required|date',
            'deliverables.*.tasks.*.end_date' => 'required|date|after_or_equal:deliverables.*.tasks.*.start_date',
            'deliverables.*.tasks.*.planned_complete' => 'required|numeric|min:0|max:100',
            'deliverables.*.tasks.*.actual_complete' => 'required|numeric|min:0|max:100',
            'deliverables.*.tasks.*.status_id' => 'required|exists:statuses,id',
            'deliverables.*.tasks.*.remarks' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            \Log::error('PD Project Update Validation Failed:', $validator->errors()->toArray());

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::beginTransaction();

            // Get user's ministry for role-based assignment
            $user = auth()->user();
            $userMinistry = $user->userMinistries()->with(['ministry', 'department'])->first();

            // Step 1: Update Project Information
            $pd_project->name = $request->name;
            $pd_project->department_id = $request->department_id;

            // Set ministry_id based on user role
            if ($user->hasRole('super admin')) {
                $pd_project->ministry_id = $request->ministry_id;
            } else {
                $pd_project->ministry_id = $userMinistry ? $userMinistry->ministry_id : $request->ministry_id;
            }

            $pd_project->psdp_no = $request->psdp_no;
            $pd_project->psdp_id = $request->psdp_id;
            $pd_project->approving_authority_id = $request->approving_authority_id;
            $pd_project->authority_approved_date = $request->authority_approved_date;
            $pd_project->admin_approval_date = $request->admin_approval_date;
            $pd_project->completion_date_pc1 = $request->completion_date_pc1;
            $pd_project->likely_completion_date = $request->likely_completion_date;
            // Convert from millions to actual amounts
            $pd_project->total_cost = $request->total_cost * 1000000;
            $pd_project->lc_amount = $request->lc_amount * 1000000;
            $pd_project->fc_amount = $request->fc_amount * 1000000;
            $pd_project->grand_amount = $request->total_cost * 1000000;
            $pd_project->last_revised_date = $request->completion_date_pc1;
            $pd_project->save();

            // Step 2: Update Budget Allocations
            // Delete existing allocations and quarters
            $pd_project->allocations()->each(function($allocation) {
                $allocation->quarters()->delete();
            });
            $pd_project->allocations()->delete();

            if ($request->has('allocations') && is_array($request->allocations)) {
                foreach ($request->allocations as $allocationData) {
                    $allocation = new Allocation();
                    $allocation->project_id = $pd_project->id;
                    $allocation->financial_year_id = $allocationData['financial_year_id'];
                    $allocation->allocation_amount = $allocationData['allocation_amount'] ?? 0;
                    $allocation->expense_amount = $allocationData['expense_amount'] ?? 0;
                    $allocation->release_amount = $allocationData['release_amount'] ?? 0;
                    $allocation->save();

                    // Save quarterly data if provided
                    if (isset($allocationData['quarters']) && is_array($allocationData['quarters'])) {
                        foreach ($allocationData['quarters'] as $quarterIndex => $quarterData) {
                            if (!empty($quarterData['release_amount']) || !empty($quarterData['expense_amount'])) {
                                $quarter = new Quarter();
                                $quarter->project_id = $pd_project->id;
                                $quarter->allocation_id = $allocation->id;
                                $quarter->quarter = 'Q' . ($quarterIndex + 1);
                                $quarter->release_amount = $quarterData['release_amount'] ?? 0;
                                $quarter->expense_amount = $quarterData['expense_amount'] ?? 0;
                                $quarter->save();
                            }
                        }
                    }
                }
            }

            // Step 3: Update Deliverables and Tasks
            // Delete existing deliverables and tasks
            $pd_project->deliverables()->each(function($deliverable) {
                $deliverable->tasks()->delete();
            });
            $pd_project->deliverables()->delete();

            if ($request->has('deliverables') && is_array($request->deliverables)) {
                foreach ($request->deliverables as $deliverableData) {
                    $deliverable = new Deliverable();
                    $deliverable->project_id = $pd_project->id;
                    $deliverable->name = $deliverableData['name'];
                    $deliverable->save();

                    // Save tasks for this deliverable
                    if (isset($deliverableData['tasks']) && is_array($deliverableData['tasks'])) {
                        foreach ($deliverableData['tasks'] as $taskData) {
                            $task = new Task();
                            $task->project_id = $pd_project->id;
                            $task->deliverable_id = $deliverable->id;
                            $task->code = $taskData['code'];
                            $task->description = $taskData['description'];
                            $task->start_date = $taskData['start_date'];
                            $task->end_date = $taskData['end_date'];
                            $task->planned_complete = $taskData['planned_complete'] ?? 0;
                            $task->actual_complete = $taskData['actual_complete'] ?? 0;
                            $task->status_id = $taskData['status_id'];
                            $task->remarks = $taskData['remarks'] ?? null;
                            $task->save();
                        }
                    }
                }
            }

            DB::commit();

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Project updated successfully!',
                    'redirect' => route('psdp.pd-projects.index')
                ]);
            }

            return redirect()->route('psdp.pd-projects.index')
                ->with('success', 'Project updated successfully.');

        } catch (\Exception $e) {
            DB::rollback();

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error updating project: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Error updating project: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Project $pd_project)
    {
        try {
            $pd_project->delete();
            return redirect()->route('psdp.pd-projects.index')
                ->with('success', 'Project deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Error deleting project: ' . $e->getMessage());
        }
    }

    // API methods for cascading dropdowns
    public function getDivisions($ministry)
    {
        $divisions = Division::where('ministry_id', $ministry)->where('isActive', true)->get();
        return response()->json($divisions);
    }

    public function getDepartments($ministry)
    {
        $departments = Department::where('ministry_id', $ministry)->where(function($query) {
            $query->where('isActive', true)->orWhereNull('isActive');
        })->get();
        return response()->json($departments);
    }

    public function getDepartmentsByDivision($division)
    {
        $departments = Department::where('division_id', $division)->where(function($query) {
            $query->where('isActive', true)->orWhereNull('isActive');
        })->get();
        return response()->json($departments);
    }
}
