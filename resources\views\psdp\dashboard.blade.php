@extends('layouts.app')

@section('title', 'PSDP Management Dashboard')

@section('content')
<div class="bg-[#ebf3f8] min-h-screen">
    <div class="p-5">
        <h2 class="text-[24px] text-[#0D163A] font-bold mt-2">PSDP Management Dashboard</h2>

        <!-- Budget Stats Cards -->
        <div class="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4 mt-8">

            <!-- Total Budget Allocation -->
            <div class="bg-[#AEE8FF] w-full h-[162px] rounded-[13px] p-5 flex flex-col justify-between">
                <div class="flex items-center gap-3 border-b border-[#40A5BA] pb-3">
                    <div class="flex justify-center items-center rounded-full w-[47px] h-[47px] border border-[#8FC3D2]">
                        <img src="{{ asset('assets/empty-wallet.png') }}" alt="">
                    </div>
                    <div class="flex flex-col gap-1">
                        <h3 class="text-[17px] text-[#1A92AA] font-semibold">Total Budget Allocation</h3>
                        <div class="flex gap-2 items-center">
                            <img src="{{ asset('assets/export.svg') }}" alt="">
                            <p class="text-[15px] text-[#1A92AA]">100% of Total Budget</p>
                        </div>
                    </div>
                </div>
                <div class="flex justify-between items-center">
                    <p class="text-[27.5px] text-[#1A92AA] font-semibold">PKR {{ number_format($budgetStats['total_budget_allocation'] / 1000000, 1) }}M</p>
                    <img src="{{ asset('assets/arrow-right.svg') }}" class="cursor-pointer">
                </div>
            </div>

            <!-- Final Allocation -->
            <div class="bg-[#FFE3C8] w-full h-[162px] rounded-[13px] p-5 flex flex-col justify-between">
                <div class="flex items-center gap-3 border-b border-[#C67639] pb-3">
                    <div class="flex justify-center items-center rounded-full w-[47px] h-[47px] border border-[#C67639]">
                        <img src="{{ asset('assets/empty-wallet-brown.svg') }}" alt="">
                    </div>
                    <div class="flex flex-col gap-1">
                        <h3 class="text-[17px] text-[#C67639] font-semibold">Final Allocation</h3>
                        <div class="flex gap-2 items-center">
                            <img src="{{ asset('assets/export-brown.svg') }}" alt="">
                            <p class="text-[15px] text-[#C67639]">{{ $budgetStats['final_allocation_percentage'] }}% of Budget</p>
                        </div>
                    </div>
                </div>
                <div class="flex justify-between items-center">
                    <p class="text-[27.5px] text-[#C67639] font-semibold">PKR {{ number_format($budgetStats['final_allocation'] / 1000000, 1) }}M</p>
                    <img src="{{ asset('assets/arrow-right-brown.svg') }}" class="cursor-pointer">
                </div>
            </div>

            <!-- Total Releases -->
            <div class="bg-[#E1CEFF] w-full h-[162px] rounded-[13px] p-5 flex flex-col justify-between">
                <div class="flex items-center gap-3 border-b border-[#7848AB] pb-3">
                    <div class="flex justify-center items-center rounded-full w-[47px] h-[47px] border border-[#7848AB]">
                        <img src="{{ asset('assets/empty-wallet-purple.svg') }}" alt="">
                    </div>
                    <div class="flex flex-col gap-1">
                        <h3 class="text-[17px] text-[#7848AB] font-semibold">Total Releases</h3>
                        <div class="flex gap-2 items-center">
                            <img src="{{ asset('assets/export-purple.svg') }}" alt="">
                            <p class="text-[15px] text-[#7848AB]">{{ $budgetStats['releases_percentage'] }}% Released</p>
                        </div>
                    </div>
                </div>
                <div class="flex justify-between items-center">
                    <p class="text-[27.5px] text-[#7848AB] font-semibold">PKR {{ number_format($budgetStats['total_releases'] / 1000000, 1) }}M</p>
                    <img src="{{ asset('assets/arrow-right-purple.svg') }}" class="cursor-pointer">
                </div>
            </div>

            <!-- Expenditure -->
            <div class="bg-[#FFC8C9] w-full h-[162px] rounded-[13px] p-5 flex flex-col justify-between">
                <div class="flex items-center gap-3 border-b border-[#C74646] pb-3">
                    <div class="flex justify-center items-center rounded-full w-[47px] h-[47px] border border-[#C74646]">
                        <img src="{{ asset('assets/empty-wallet-red.svg') }}" alt="">
                    </div>
                    <div class="flex flex-col gap-1">
                        <h3 class="text-[17px] text-[#C74646] font-semibold">Expenditure</h3>
                        <div class="flex gap-2 items-center">
                            <img src="{{ asset('assets/export-red.svg') }}" alt="">
                            <p class="text-[15px] text-[#C74646]">{{ $budgetStats['expenditure_percentage'] }}% Utilized</p>
                        </div>
                    </div>
                </div>
                <div class="flex justify-between items-center">
                    <p class="text-[27.5px] text-[#C74646] font-semibold">PKR {{ number_format($budgetStats['expenditure'] / 1000000, 1) }}M</p>
                    <img src="{{ asset('assets/arrow-right-red.svg') }}" class="cursor-pointer">
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="p-5 flex gap-5">
        <!-- Department-wise Allocation Chart -->
        <div class="w-[30%] bg-white p-4 rounded-md">
            <div class="pb-3 border-b border-[#0D163A]">
                <p class="text-[#0D163A] font-bold text-[25px]">Dept-wise Allocation</p>
            </div>
            <div id="donut" class="mt-6"></div>
        </div>

        <!-- Budget vs Expenditure Chart -->
        <div class="w-[70%] bg-white p-4 rounded-md">
            <div class="w-full">
                <p class="text-[#0D163A] font-bold text-[25px]">Budget vs Expenditure</p>
                <div class="w-full flex justify-end">
                    <div class="flex gap-4">
                        <div class="flex gap-3 items-center">
                            <div class="h-[14px] w-[14px] rounded-full bg-[#7DD587]"></div>
                            <p>Budget</p>
                        </div>
                        <div class="flex gap-3 items-center">
                            <div class="h-[14px] w-[14px] rounded-full bg-[#E8635E]"></div>
                            <p>Expenditure</p>
                        </div>
                        <select id="quarterFilter" class="w-40 bg-[#EBF3F8] rounded-full px-3 py-2 text-sm border-0 focus:outline-none focus:ring-2 focus:ring-[#1A92AA] cursor-pointer">
                            <option value="all">All Quarters</option>
                            <option value="Q1">Q1 Only</option>
                            <option value="Q2">Q2 Only</option>
                            <option value="Q3">Q3 Only</option>
                            <option value="Q4">Q4 Only</option>
                        </select>
                    </div>
                </div>
            </div>
            <div id="chart3" class="w-full"></div>
        </div>
    </div>

    <!-- Project-wise Financial Overview -->
    <div class="p-5">
        <div class="w-full bg-white rounded-md p-5">
            <div class="space-y-4">
                <!-- Search and Sort -->
                <div class="flex flex-col lg:flex-row items-stretch lg:items-center space-y-3 lg:space-y-0 lg:space-x-4">
                    <!-- Search input -->
                    <div class="flex items-center bg-[#f1f8fc] rounded-full px-4 py-3 flex-1 lg:flex-[3] hover:bg-[#e8f4f8] transition-colors duration-200">
                        <img src="{{ asset('assets/search-normal.svg') }}" alt="" class="w-4 h-4 mr-3 text-gray-400 flex-shrink-0">
                        <input type="text"
                               id="projectSearch"
                               placeholder="Search projects by name..."
                               class="bg-transparent focus:outline-none outline-none focus:ring-0 border-0 w-full text-sm text-gray-700 placeholder-gray-500" />
                        <button id="clearSearch" class="ml-2 text-gray-400 hover:text-gray-600 transition-colors duration-200 hidden">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <!-- Sort dropdown -->
                    <div class="flex-1 lg:flex-[2]">
                        <div class="relative">
                            <select id="sortBy" class="bg-[#f1f8fc] w-full text-gray-600 text-sm rounded-full px-4 py-3 pr-10 border-0 focus:outline-none focus:ring-2 focus:ring-[#1A92AA] focus:bg-white appearance-none cursor-pointer hover:bg-[#e8f4f8] transition-colors duration-200">
                                <option value="name">Sort by: Project Name</option>
                                <option value="total_cost">Sort by: Total Cost</option>
                                <option value="allocation">Sort by: Allocation</option>
                                <option value="releases">Sort by: Releases</option>
                                <option value="expenditure">Sort by: Expenditure</option>
                            </select>
                            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3 text-gray-400">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <!-- Reset button -->
                    <div class="flex-shrink-0">
                        <button id="resetFilters" class="bg-[#1A92AA] text-white px-4 py-3 rounded-full text-sm font-medium hover:bg-[#157a94] transition-colors duration-200 flex items-center space-x-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            <span>Reset</span>
                        </button>
                    </div>
                </div>

                <!-- Heading and Legend -->
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                    <!-- Heading -->
                    <h2 class="text-[24px] font-semibold text-[#0D163A]">
                        Project-wise Financial Overview (in Millions PKR)
                    </h2>
                    <!-- Legend -->
                    <div class="flex flex-wrap items-center gap-4">
                        <!-- Total Budget -->
                        <div class="flex items-center space-x-2">
                            <span class="w-3 h-3 rounded-full bg-[#7CD47F]"></span>
                            <span class="text-sm text-gray-600">Total Cost</span>
                        </div>
                        <!-- Allocated -->
                        <div class="flex items-center space-x-2">
                            <span class="w-3 h-3 rounded-full bg-[#1A92AA]"></span>
                            <span class="text-sm text-gray-600">Allocation</span>
                        </div>
                        <!-- Released -->
                        <div class="flex items-center space-x-2">
                            <span class="w-3 h-3 rounded-full bg-[#F9BA33]"></span>
                            <span class="text-sm text-gray-600">Released</span>
                        </div>
                        <!-- Expenditure -->
                        <div class="flex items-center space-x-2">
                            <span class="w-3 h-3 rounded-full bg-[#CE6E6E]"></span>
                            <span class="text-sm text-gray-600">Expenditure</span>
                        </div>
                    </div>
                </div>
            </div>
            <div id="chart" class="w-full mt-3"></div>
        </div>
    </div>

    <!-- View Projects Button -->
    <a href="{{ route('psdp.projects.index') }}">
        <button class="px-8 py-3 bg-[#1A92AA] text-white rounded-full mb-5 mx-auto block cursor-pointer">
            View PSDP Projects
        </button>
    </a>
</div>

<!-- Include ApexCharts -->
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>

<script>
// Project-wise Financial Overview Chart
var projectFinancialOptions = {
    series: [{
        name: 'Total Cost',
        data: [
            @foreach($projectFinancialData as $project)
                {{ $project['total_cost'] / 1000000 }},
            @endforeach
        ]
    }, {
        name: 'Allocation',
        data: [
            @foreach($projectFinancialData as $project)
                {{ $project['allocation'] / 1000000 }},
            @endforeach
        ]
    }, {
        name: 'Released',
        data: [
            @foreach($projectFinancialData as $project)
                {{ $project['releases'] / 1000000 }},
            @endforeach
        ]
    }, {
        name: 'Expenditure',
        data: [
            @foreach($projectFinancialData as $project)
                {{ $project['expenditure'] / 1000000 }},
            @endforeach
        ]
    }],
    chart: {
        type: 'bar',
        height: 350
    },
    legend: {
        show: false
    },
    colors: ['#7CD47F', '#1A92AA', '#F9BA33', '#CE6E6E'],
    plotOptions: {
        bar: {
            horizontal: false,
            columnWidth: '50%',
            borderRadius: 5,
            borderRadiusApplication: 'end'
        },
    },
    dataLabels: {
        enabled: false
    },
    stroke: {
        show: true,
        width: 2,
        colors: ['transparent']
    },
    xaxis: {
        categories: [
            @foreach($projectFinancialData as $project)
                '{{ substr($project['name'], 0, 10) }}',
            @endforeach
        ],
    },
    yaxis: {
        labels: {
            formatter: function (val) {
                return val + 'M';
            },
            style: {
                fontSize: '12px',
                colors: '#6B7280'
            }
        },
        min: 0,
        tickAmount: 5
    },
    grid: {
        show: true,
        borderColor: '#E5E7EB',
        strokeDashArray: 3,
        xaxis: {
            lines: {
                show: false
            }
        },
        yaxis: {
            lines: {
                show: true
            }
        }
    },
    fill: {
        opacity: 1
    },
};

var projectChart = new ApexCharts(document.querySelector("#chart"), projectFinancialOptions);
projectChart.render();

// Quarterly Budget vs Expenditure Chart
let originalQuarterlyData = [
    @foreach($quarterlyData as $quarter)
    {
        quarter: '{{ $quarter->quarter }}',
        budget: {{ $quarter->budget / 1000 }},
        expenditure: {{ $quarter->expenditure / 1000 }}
    },
    @endforeach
];

var quarterlyOptions = {
    series: [{
        name: 'Budget',
        data: originalQuarterlyData.map(q => q.budget)
    }, {
        name: 'Expenditure',
        data: originalQuarterlyData.map(q => q.expenditure)
    }],
    chart: {
        type: 'bar',
        height: 350,
        animations: {
            enabled: true,
            easing: 'easeinout',
            speed: 800
        }
    },
    colors: ['#7DD587', '#E8635E'],
    plotOptions: {
        bar: {
            horizontal: false,
            columnWidth: '50%',
            borderRadius: 5,
            borderRadiusApplication: 'end'
        },
    },
    dataLabels: {
        enabled: false
    },
    stroke: {
        show: true,
        width: 2,
        colors: ['transparent']
    },
    xaxis: {
        categories: originalQuarterlyData.map(q => q.quarter),
    },
    yaxis: {
        labels: {
            formatter: function (val) {
                return val + 'K';
            },
            style: {
                fontSize: '12px',
                colors: '#6B7280'
            }
        },
        min: 0,
        tickAmount: 5
    },
    grid: {
        show: true,
        borderColor: '#E5E7EB',
        strokeDashArray: 3,
        xaxis: {
            lines: {
                show: false
            }
        },
        yaxis: {
            lines: {
                show: true
            }
        }
    },
    fill: {
        opacity: 1
    },
    tooltip: {
        y: {
            formatter: function (val) {
                return val + 'K PKR';
            }
        }
    }
};

var quarterlyChart = new ApexCharts(document.querySelector("#chart3"), quarterlyOptions);
quarterlyChart.render();

// Quarter filter functionality
document.getElementById('quarterFilter').addEventListener('change', function() {
    const selectedQuarter = this.value;
    let filteredData = originalQuarterlyData;

    if (selectedQuarter !== 'all') {
        filteredData = originalQuarterlyData.filter(q => q.quarter === selectedQuarter);
    }

    const newQuarterlyOptions = {
        ...quarterlyOptions,
        series: [{
            name: 'Budget',
            data: filteredData.map(q => q.budget)
        }, {
            name: 'Expenditure',
            data: filteredData.map(q => q.expenditure)
        }],
        xaxis: {
            categories: filteredData.map(q => q.quarter)
        }
    };

    quarterlyChart.updateOptions(newQuarterlyOptions);
});

// Department-wise Allocation Donut Chart
var donutOptions = {
    series: [
        @foreach($departmentAllocations as $dept)
            {{ $dept->total_allocation }},
        @endforeach
    ],
    chart: {
        type: 'donut',
        height: 350
    },
    colors: ['#75C9D9', '#99E194', '#FFD388', '#F29797', '#B085DD'],
    labels: [
        @foreach($departmentAllocations as $dept)
            '{{ $dept->department }}',
        @endforeach
    ],
    legend: {
        show: true,
        position: 'bottom',
    },
    responsive: [{
        breakpoint: 480,
        options: {
            chart: {
                width: 200
            },
            legend: {
                position: 'bottom'
            }
        }
    }]
};

var donutChart = new ApexCharts(document.querySelector("#donut"), donutOptions);
donutChart.render();

// Search and Sort Functionality
let originalProjectData = [
    @foreach($projectFinancialData as $index => $project)
    {
        name: '{{ $project['name'] }}',
        total_cost: {{ $project['total_cost'] / 1000000 }},
        allocation: {{ $project['allocation'] / 1000000 }},
        releases: {{ $project['releases'] / 1000000 }},
        expenditure: {{ $project['expenditure'] / 1000000 }},
        index: {{ $index }}
    },
    @endforeach
];

let filteredData = [...originalProjectData];

// Search functionality
const searchInput = document.getElementById('projectSearch');
const clearButton = document.getElementById('clearSearch');

searchInput.addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();

    // Show/hide clear button
    if (searchTerm.length > 0) {
        clearButton.classList.remove('hidden');
    } else {
        clearButton.classList.add('hidden');
    }

    // Filter data
    filteredData = originalProjectData.filter(project =>
        project.name.toLowerCase().includes(searchTerm)
    );
    updateChart();
    updateResultsCount();
});

// Clear search functionality
clearButton.addEventListener('click', function() {
    searchInput.value = '';
    clearButton.classList.add('hidden');
    filteredData = [...originalProjectData];
    updateChart();
    updateResultsCount();
    searchInput.focus();
});

// Sort functionality
document.getElementById('sortBy').addEventListener('change', function(e) {
    const sortBy = e.target.value;

    filteredData.sort((a, b) => {
        if (sortBy === 'name') {
            return a.name.localeCompare(b.name);
        } else {
            return b[sortBy] - a[sortBy]; // Descending order for numerical values
        }
    });

    updateChart();
});

// Update chart with filtered/sorted data
function updateChart() {
    const newOptions = {
        ...projectFinancialOptions,
        series: [{
            name: 'Total Cost',
            data: filteredData.map(project => project.total_cost)
        }, {
            name: 'Allocation',
            data: filteredData.map(project => project.allocation)
        }, {
            name: 'Released',
            data: filteredData.map(project => project.releases)
        }, {
            name: 'Expenditure',
            data: filteredData.map(project => project.expenditure)
        }],
        xaxis: {
            categories: filteredData.map(project => project.name.length > 10 ? project.name.substring(0, 10) + '...' : project.name)
        }
    };

    projectChart.updateOptions(newOptions);
}

// Add loading state for better UX
function showLoading() {
    document.getElementById('chart').innerHTML = '<div class="flex justify-center items-center h-64"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div></div>';
}

// Reset button functionality
document.getElementById('resetFilters').addEventListener('click', function() {
    searchInput.value = '';
    clearButton.classList.add('hidden');
    document.getElementById('sortBy').value = 'name';
    filteredData = [...originalProjectData];
    updateChart();
    updateResultsCount();

    // Add visual feedback
    this.innerHTML = '<svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg><span>Resetting...</span>';

    setTimeout(() => {
        this.innerHTML = '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg><span>Reset</span>';
    }, 500);
});

// Update results count
function updateResultsCount() {
    const count = filteredData.length;
    const total = originalProjectData.length;

    // Update heading with results count
    const heading = document.querySelector('h2');
    if (count < total) {
        heading.innerHTML = `Project-wise Financial Overview (${count} of ${total} projects)`;
    } else {
        heading.innerHTML = 'Project-wise Financial Overview (in Millions PKR)';
    }
}

// Add keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + K to focus search
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        searchInput.focus();
        searchInput.select();
    }

    // Escape to clear search
    if (e.key === 'Escape') {
        if (searchInput.value) {
            searchInput.value = '';
            clearButton.classList.add('hidden');
            filteredData = [...originalProjectData];
            updateChart();
            updateResultsCount();
        }
    }

    // Ctrl/Cmd + R to reset filters
    if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
        e.preventDefault();
        document.getElementById('resetFilters').click();
    }
});

// Add search tips
searchInput.addEventListener('focus', function() {
    this.placeholder = 'Type to search... (Ctrl+K to focus, Esc to clear)';
});

searchInput.addEventListener('blur', function() {
    this.placeholder = 'Search projects by name...';
});

// Initialize results count on page load
document.addEventListener('DOMContentLoaded', function() {
    updateResultsCount();
});
</script>

@endsection
