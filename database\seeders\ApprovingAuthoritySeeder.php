<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\PSDP\ApprovingAuthority;

class ApprovingAuthoritySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $authorities = [
            ['id' => 1, 'name' => 'DDWP'],
            ['id' => 2, 'name' => 'CDWP'],
            ['id' => 3, 'name' => 'ECNEC'],
        ];

        foreach ($authorities as $authority) {
            ApprovingAuthority::updateOrCreate(
                ['id' => $authority['id']],
                $authority
            );
        }
    }
}
