<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.jsdelivr.net/npm/flowbite@3.1.2/dist/flowbite.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script src="https://cdn.jsdelivr.net/npm/flowbite@3.1.2/dist/flowbite.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <title>MOITT</title>
</head>
<body class="bg-[#ebf3f8]">

    <?php include "./common/header.html" ?>

    <div class="p-5">
        <div class="bg-white w-full rounded-[14px]">

            <div class="text-center my-3 p-5">

                <h2 class="text-[#0D163A] text-[25px] font-semibold">Smart Office</h2>
                <p class="text-[16px]">Federal Ministries & Departments PSDP through MoITT (NITB)</p>

            </div>

            <div class="w-full mx-auto bg-[#DBEEF2] py-3 flex gap-10 justify-between items-center">

            <div class="grid grid-cols-2 md:grid-cols-4 w-[80%] mx-auto ">
            
                <div class="flex items-center gap-3">

                <img src="./assets/approval-icon.svg" class="w-[32px] h-[32px]">
                <div class="font-medium ">
                    <p class="text-md ">Approval Forum</p>
                    <p class="text-lg font-bold -my-1">DDWP</p>
                    <p class="text-xs">30-06-2025</p>
                </div>

                </div>

                <div class="flex items-center gap-3">

                <img src="./assets/start-date.svg" class="w-[32px] h-[32px]">
                <div class="font-medium ">
                    <p class="text-sm ">Start Date</p>
                    <p class="text-lg font-bold leading-6">30-06-2025</p>
                </div>

                </div>


                <div class="flex items-center gap-3">

                <img src="./assets/end-date.svg" class="w-[32px] h-[32px]">
                <div class="font-medium ">
                    <p class="text-sm ">Completion date as per PC-I</p>
                    <p class="text-lg font-bold leading-6">31-12-2025</p>
                </div>

                </div>

                <div class="flex items-center gap-3">

                <img src="./assets/completion-date.svg" class="w-[32px] h-[32px]">
                <div class="font-medium ">
                    <p class="text-sm ">Likely Completion date</p>
                    <p class="text-lg font-bold leading-6">30-06-2025</p>
                </div>

                </div>

            </div>
                

            </div>
            

            <div class="py-12 px-8">

                <div class="flex items-center gap-10">

                <div class="w-[35%] flex flex-col gap-5">

                    
                    <div class="w-full border border-[#D6DCE4] rounded-[10px]">

                        <div class="flex items-center justify-between px-4 pt-4 pb-8 border-b border-[#B6B6B6]">
                            <div class="flex items-center gap-2">
                                <img src="./assets/coin-icon.svg" alt="">
                                <p class="text-[18px] font-semibold">Project Cost</p>
                            </div>

                            <div class="text-white text-[18px] font-semibold bg-[#1A92AA] px-6 py-2 rounded-full cursor-pointer">Rs. 572.80 M</div>
                            
                        </div>

                        <div class="flex px-4  justify-between">
                            <div class="flex flex-col justify-center gap-3">
                                <p class="text-[12px] text-[#0FAE96] font-500">Utilization</p>
                                <p class="text-[18px] font-bold">78%</p>
                            </div>
                            <div id="chart4" class="w-[112px]"></div>
                        </div>

                    </div>



                    <div class="w-full border border-[#D6DCE4] rounded-[10px]">
                        <div class="p-5">
                            <p class="text-center text-[16px] mb-3">Current FY</p>

                            <div class="flex flex-col gap-2">
                            
                            <div class="flex flex-col gap-3">

                                <div class="flex items-center justify-center gap-3">
                                    <p class="mr-2 w-25 text-right">Allocation</p>
                                    <div class="px-12 text-white py-2 bg-[#F29797] rounded-full">Rs. 300.00 M</div>
                                </div>
                                
                            </div>

                            <div class="flex flex-col gap-3">

                                <div class="flex items-center justify-center gap-3">
                                    <p class="mr-2 w-25 text-right">Releases</p>
                                    <div class="px-12 text-white py-2 bg-[#FFD388] rounded-full">Rs. 281.41 M</div>
                                </div>
                                
                            </div>

                            <div class="flex flex-col gap-3">

                                <div class="flex items-center justify-center gap-3">
                                    <p class="mr-2 w-25 text-right">Expenditure</p>
                                    <div class="px-12 text-white py-2 bg-[#A7A0F1] rounded-full">Rs. 177.86 M</div>
                                </div>
                                
                            </div>

                            </div>

                        </div>
                    </div>
                    
                    </div>

                    
                    
                    <div class="w-[65%] ">
                        <div class="w-full border border-[#D6DCE4] p-4 rounded-[10px]">
                            <p class="text-[18px] font-semibold">Current Status</p>


                            <div id="chart5"></div>
                            
                        </div>
                    </div>

                    
                </div>
                
            </div>
            
            
            <div class="px-8 py-12">
                <p class="text-center font-semibold text-[25px] py-4">Overall Progress</p>

                <div class="w-full flex gap-10 mt-5">

                    <div class="p-10 w-[50%] border border-[#D6DCE4] rounded-[10px] flex gap-4 flex-col justify-center items-center">

                        <p class="text-[25px] font-semibold">Total Releases for Project</p>

                        <div class="px-12 py-3 font-semibold bg-[#A8E0F1] rounded-full">
                            Total   Rs. 300.000 M
                        </div>
                        
                    </div>

                    
                    <div class="p-10 w-[50%] border border-[#D6DCE4] rounded-[10px] flex gap-4 flex-col justify-center items-center">

                        <p class="text-[25px] font-semibold">Total Expenditure for Project</p>

                        <div class="px-12 py-3 font-semibold bg-[#F8C8C0] rounded-full">
                            Total   Rs. 132.666 M
                        </div>
                        
                    </div>

                    
                </div>


                <div class="w-full flex gap-10 mt-5">

                    <div class="w-[50%] p-10 flex flex-col justify-center items-center border border-[#D6DCE4] rounded-[10px]">

                        <div id="radialChart"></div>

                        <p class="text-[25px] font-bold">Physical Allocation</p>
                        
                    </div>
                    
                    
                    <div class="w-[50%] p-10 flex flex-col justify-center items-center border border-[#D6DCE4] rounded-[10px]">
                        <div id="radialChart2"></div>

                        <p class="text-[25px] font-bold">Financial Progress</p>
                    </div>
                    
                </div>
                
                
                
            </div>
            
            
        </div>
    </div>
    

<script>
        const radialOption = {
            series: [80],
            chart: {
                height: 250,
                type: 'radialBar',
                toolbar: {
                    show: false
                }
            },
            plotOptions: {
                radialBar: {
                    startAngle: -90,
                    endAngle: 270,
                    hollow: {
                        margin: 15,
                        size: '65%',
                        background: 'transparent',
                        position: 'front',
                        dropShadow: {
                            enabled: false
                        }
                    },
                    track: {
                        background: '#F3F4F6',
                        strokeWidth: '100%',
                        margin: 0,
                        dropShadow: {
                            enabled: false
                        }
                    },
                    dataLabels: {
                        show: true,
                        name: {
                            show: false
                        },
                        value: {
                            formatter: function(val) {
                                return parseInt(val) + '%';
                            },
                            color: '#1f2937',
                            fontSize: '30px',
                            fontWeight: 'bold',
                            show: true,
                            offsetY: 10
                        }
                    }
                }
            },
            fill: {
                type: 'gradient',
                gradient: {
                    shade: 'light',
                    type: 'horizontal',
                    shadeIntensity: 0.5,
                    gradientToColors: ['#EE201C'], // End color (orange)
                    inverseColors: false,
                    opacityFrom: 1,
                    opacityTo: 1,
                    stops: [0, 100],
                    colorStops: [
                        {
                            offset: 0,
                            color: '#FCD34D', // Light yellow/gold
                            opacity: 1
                        },
                        {
                            offset: 50,
                            color: '#F59E0B', // Medium orange
                            opacity: 1
                        },
                        {
                            offset: 100,
                            color: '#D97706', // Darker orange
                            opacity: 1
                        }
                    ]
                }
            },
            stroke: {
                lineCap: 'round',
                width: 8
            },
            labels: ['Progress'],
            colors: ['#F59E0B']
        };

        const radialChart = new ApexCharts(document.querySelector("#radialChart"), radialOption);
        radialChart.render();
</script>

<script>
        const radialOption2 = {
            series: [80],
            chart: {
                height: 250,
                type: 'radialBar',
                toolbar: {
                    show: false
                }
            },
            plotOptions: {
                radialBar: {
                    startAngle: -90,
                    endAngle: 270,
                    hollow: {
                        margin: 15,
                        size: '65%',
                        background: 'transparent',
                        position: 'front',
                        dropShadow: {
                            enabled: false
                        }
                    },
                    track: {
                        background: '#F3F4F6',
                        strokeWidth: '100%',
                        margin: 0,
                        dropShadow: {
                            enabled: false
                        }
                    },
                    dataLabels: {
                        show: true,
                        name: {
                            show: false
                        },
                        value: {
                            formatter: function(val) {
                                return parseInt(val) + '%';
                            },
                            color: '#1f2937',
                            fontSize: '30px',
                            fontWeight: 'bold',
                            show: true,
                            offsetY: 10
                        }
                    }
                }
            },
            fill: {
                type: 'gradient',
                gradient: {
                    shade: 'light',
                    type: 'horizontal',
                    shadeIntensity: 0.5,
                    gradientToColors: ['#EE201C'], // End color (orange)
                    inverseColors: false,
                    opacityFrom: 1,
                    opacityTo: 1,
                    stops: [0, 100],
                    colorStops: [
                        {
                            offset: 0,
                            color: '#F08080', // Light yellow/gold
                            opacity: 1
                        },
                        {
                            offset: 50,
                            color: '#EE201C', // Medium orange
                            opacity: 1
                        },
                        {
                            offset: 100,
                            color: '#F08080', // Darker orange
                            opacity: 1
                        }
                    ]
                }
            },
            stroke: {
                lineCap: 'round',
                width: 8
            },
            labels: ['Progress'],
            colors: ['#F59E0B']
        };

        const radialChart2 = new ApexCharts(document.querySelector("#radialChart2"), radialOption2);
        radialChart2.render();
</script>
    
    
    
    <script>
        const options5 = {
            series: [{
                name: 'Amount',
                data: [27, 30, 15, 30, 5]
            }],
            chart: {
                type: 'bar',
                height: 400,
                toolbar: {
                    show: false
                },
                background: 'transparent'
            },
            colors: [
                '#8B5CF6', // Purple for Allocation
                '#10B981', // Green for Financial Allocation  
                '#F59E0B', // Orange for Releases
                '#3B82F6', // Blue for Surrender
                '#EF4444'  // Red for Expenditure
            ],
            plotOptions: {
                bar: {
                    horizontal: false,
                    columnWidth: '60%',
                    borderRadius: 8,
                    borderRadiusApplication: 'end',
                    distributed: true // This makes each bar use a different color
                }
            },
            dataLabels: {
                enabled: false
            },
            stroke: {
                show: false
            },
            xaxis: {
                categories: ['Allocation', 'Financial Allocation', 'Releases', 'Surrender', 'Expenditure'],
                labels: {
                    style: {
                        fontSize: '12px',
                        fontWeight: 500,
                        colors: '#374151'
                    }
                },
                axisBorder: {
                    show: false
                },
                axisTicks: {
                    show: false
                }
            },
            yaxis: {
                labels: {
                    formatter: function (val) {
                        return val + 'M';
                    },
                    style: {
                        fontSize: '12px',
                        colors: '#6B7280'
                    }
                },
                min: 0,
                max: 50,
                tickAmount: 5
            },
            grid: {
                show: true,
                borderColor: '#E5E7EB',
                strokeDashArray: 3,
                xaxis: {
                    lines: {
                        show: false
                    }
                },
                yaxis: {
                    lines: {
                        show: true
                    }
                }
            },
            legend: {
                show: true,
                position: 'bottom',
                horizontalAlign: 'center',
                fontSize: '12px',
                fontWeight: 500,
                labels: {
                    colors: '#374151'
                },
                markers: {
                    width: 12,
                    height: 12,
                    radius: 2
                },
                itemMargin: {
                    horizontal: 15,
                    vertical: 5
                }
            },
            tooltip: {
                y: {
                    formatter: function (val) {
                        return val + 'M';
                    }
                }
            },
            

        };

        const chart5 = new ApexCharts(document.querySelector("#chart5"), options5);
        chart5.render();
    </script>
    
    
    
<script>
// Fourth chart (chart4) - same as chart2
const options4 = {
    series: [{
        name: 'Data Series',
        data: [30, 45, 39, 55, 50, 64]
    }],
    chart: {
        type: 'area',
        height: 100,
        toolbar: {
            show: false
        },
        zoom: {
            enabled: false
        }
    },
    stroke: {
        curve: 'smooth',
        width: 3,
        colors: ['#14B8A6']
    },
    fill: {
        type: 'gradient',
        gradient: {
            shade: 'light',
            type: 'vertical',
            shadeIntensity: 0.5,
            gradientToColors: ['transparent'],
            inverseColors: false,
            opacityFrom: 0.4,
            opacityTo: 0,
            stops: [0, 100]
        }
    },
    colors: ['#14B8A6'],
    grid: {
        show: false
    },
    xaxis: {
        labels: {
            show: false
        },
        axisBorder: {
            show: false
        },
        axisTicks: {
            show: false
        },
        categories: [1, 2, 3, 4] // Updated to match 4 data points
    },
    yaxis: {
        labels: {
            show: false
        }
    },
    tooltip: {
        enabled: true
    },
    markers: {
        size: 0
    },
    dataLabels: {
        enabled: false
    }
};

const chart4 = new ApexCharts(document.querySelector("#chart4"), options4);
chart4.render();
</script>
    
</body>
</html>