<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PSDP\Project;
use App\Models\PSDP\Ministry;
use App\Models\PSDP\Department;
use App\Models\PSDP\ApprovingAuthority;
use App\Models\PSDP\FinancialYear;
use App\Models\PSDP\Allocation;
use App\Models\PSDP\Quarter;
use App\Models\PSDP\Deliverable;
use App\Models\PSDP\Task;
use App\Models\PSDP\Status;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class PSDPDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $csvFile = base_path('PSDP_Data.csv');

        if (!file_exists($csvFile)) {
            $this->command->error('PSDP_Data.csv file not found in project root!');
            return;
        }

        $this->command->info('Starting PSDP Data import...');

        try {
            DB::beginTransaction();

            // Get required entities
            $ministry = $this->getOrCreateMinistry();
            $nitbDepartment = $this->getOrCreateDepartment('National Information Technology Board (NITB)', $ministry->id);
            $psebDepartment = $this->getOrCreateDepartment('Pakistan Software Export Board (PSEB)', $ministry->id);
            $itWingDepartment = $this->getOrCreateDepartment('IT Wing', $ministry->id);

            $ddwpAuthority = $this->getOrCreateApprovingAuthority('DDWP');
            $ecnecAuthority = $this->getOrCreateApprovingAuthority('ECNEC');
            $cdwpAuthority = $this->getOrCreateApprovingAuthority('CDWP');

            $fy2024_25 = $this->getOrCreateFinancialYear('2024-2025');
            $fy2025_26 = $this->getOrCreateFinancialYear('2025-2026');

            $defaultStatus = $this->getOrCreateStatus('In Progress');

            // Get PD users
            $pdNitb = User::where('email', '<EMAIL>')->first();
            $pdPseb = User::where('email', '<EMAIL>')->first();

            if (!$pdNitb || !$pdPseb) {
                $this->command->error('PD users not found! Please run UserSeeder first.');
                return;
            }

            // Parse CSV and create projects
            $handle = fopen($csvFile, 'r');
            $lineNumber = 0;
            $projectsCreated = 0;

            while (($data = fgetcsv($handle, 1000, ',')) !== FALSE) {
                $lineNumber++;

                // Skip header rows and empty rows
                if ($lineNumber <= 7 || empty($data[0]) || !is_numeric($data[0])) {
                    continue;
                }

                try {
                    $projectData = $this->parseProjectData($data);

                    if (!$projectData) {
                        continue;
                    }

                    // Determine department and PD user based on project name
                    $department = $nitbDepartment; // Default
                    $pdUser = $pdNitb; // Default

                    if (stripos($projectData['name'], 'PSEB') !== false) {
                        $department = $psebDepartment;
                        $pdUser = $pdPseb;
                    } elseif (stripos($projectData['name'], 'IT Wing') !== false) {
                        $department = $itWingDepartment;
                        $pdUser = $pdNitb; // Assign to NITB PD for now
                    }

                    // Determine approving authority
                    $authority = $ddwpAuthority; // Default
                    if (stripos($projectData['approval_forum'], 'ECNEC') !== false) {
                        $authority = $ecnecAuthority;
                    } elseif (stripos($projectData['approval_forum'], 'CDWP') !== false) {
                        $authority = $cdwpAuthority;
                    }

                    // Create project
                    $project = Project::create([
                        'name' => $projectData['name'],
                        'ministry_id' => $ministry->id,
                        'department_id' => $department->id,
                        'psdp_no' => $projectData['psdp_no'],
                        'psdp_id' => 'PSDP-' . $projectData['psdp_no'] . '-' . date('Y'),
                        'approving_authority_id' => $authority->id,
                        'authority_approved_date' => $projectData['approval_date'],
                        'admin_approval_date' => $projectData['approval_date'],
                        'completion_date_pc1' => $projectData['completion_date_pc1'],
                        'likely_completion_date' => $projectData['likely_completion_date'],
                        'total_cost' => $projectData['total_cost'],
                        'lc_amount' => $projectData['total_cost'], // Assuming all local currency
                        'fc_amount' => 0,
                        'grand_amount' => $projectData['total_cost'],
                        'last_revised_date' => $projectData['completion_date_pc1'],
                        'added_by_user_id' => $pdUser->id,
                    ]);

                    // Create allocation for FY 2024-25
                    if ($projectData['allocation_amount'] > 0) {
                        $allocation = Allocation::create([
                            'project_id' => $project->id,
                            'financial_year_id' => $fy2024_25->id,
                            'allocation_amount' => $projectData['allocation_amount'],
                            'release_amount' => $projectData['release_amount'],
                            'expense_amount' => $projectData['expense_amount'],
                        ]);

                        // Create quarterly breakdown (distribute equally across quarters)
                        $quarterlyRelease = $projectData['release_amount'] / 4;
                        $quarterlyExpense = $projectData['expense_amount'] / 4;

                        for ($q = 1; $q <= 4; $q++) {
                            Quarter::create([
                                'project_id' => $project->id,
                                'allocation_id' => $allocation->id,
                                'quarter' => 'Q' . $q,
                                'release_amount' => $quarterlyRelease,
                                'expense_amount' => $quarterlyExpense,
                            ]);
                        }
                    }

                    // Create default deliverable and task
                    $deliverable = Deliverable::create([
                        'project_id' => $project->id,
                        'name' => 'Project Implementation',
                    ]);

                    Task::create([
                        'project_id' => $project->id,
                        'deliverable_id' => $deliverable->id,
                        'code' => '1.1',
                        'description' => 'Complete project implementation as per PC-1',
                        'start_date' => $projectData['approval_date'],
                        'end_date' => $projectData['completion_date_pc1'],
                        'planned_complete' => 100,
                        'actual_complete' => $this->calculateActualProgress($projectData),
                        'status_id' => $defaultStatus->id,
                        'remarks' => 'Auto-generated from PSDP data',
                    ]);

                    $projectsCreated++;
                    $this->command->info("Created project: {$project->name}");

                } catch (\Exception $e) {
                    $this->command->error("Error processing line {$lineNumber}: " . $e->getMessage());
                    Log::error("PSDP Seeder Error on line {$lineNumber}: " . $e->getMessage(), [
                        'data' => $data,
                        'exception' => $e
                    ]);
                }
            }

            fclose($handle);

            DB::commit();
            $this->command->info("Successfully imported {$projectsCreated} projects from PSDP data!");

        } catch (\Exception $e) {
            DB::rollback();
            $this->command->error('Error importing PSDP data: ' . $e->getMessage());
            Log::error('PSDP Seeder Error: ' . $e->getMessage(), ['exception' => $e]);
        }
    }

    private function parseProjectData($data)
    {
        try {
            // Skip if essential data is missing
            if (empty($data[1]) || empty($data[2])) {
                return null;
            }

            $approvalInfo = $this->parseApprovalInfo($data[3] ?? '');

            return [
                'psdp_no' => trim($data[1]),
                'name' => trim($data[2]),
                'approval_forum' => $approvalInfo['forum'],
                'approval_date' => $approvalInfo['date'],
                'completion_date_pc1' => $this->parseDate($data[4] ?? null),
                'likely_completion_date' => $this->parseDate($data[5] ?? null),
                'total_cost' => $this->parseAmount($data[6] ?? 0) * 1000000, // Convert millions to actual amount
                'allocation_amount' => $this->parseAmount($data[8] ?? 0) * 1000000,
                'release_amount' => $this->parseAmount($data[18] ?? 0) * 1000000,
                'expense_amount' => $this->parseAmount($data[20] ?? 0) * 1000000,
            ];
        } catch (\Exception $e) {
            Log::error('Error parsing project data: ' . $e->getMessage(), ['data' => $data]);
            return null;
        }
    }

    private function parseApprovalInfo($approvalString)
    {
        $lines = explode("\n", trim($approvalString));
        $forum = trim($lines[0] ?? 'DDWP');
        $dateString = trim($lines[1] ?? '');

        $date = null;
        if ($dateString) {
            try {
                $date = Carbon::createFromFormat('d-m-Y', $dateString)->format('Y-m-d');
            } catch (\Exception $e) {
                $date = now()->format('Y-m-d');
            }
        }

        return [
            'forum' => $forum,
            'date' => $date
        ];
    }

    private function parseDate($dateString)
    {
        if (empty($dateString)) {
            return null;
        }

        try {
            return Carbon::createFromFormat('d-m-Y', trim($dateString))->format('Y-m-d');
        } catch (\Exception $e) {
            try {
                return Carbon::createFromFormat('Y-m-d', trim($dateString))->format('Y-m-d');
            } catch (\Exception $e2) {
                return null;
            }
        }
    }

    private function parseAmount($amountString)
    {
        if (empty($amountString)) {
            return 0;
        }

        // Remove any non-numeric characters except decimal point and minus
        $cleaned = preg_replace('/[^\d.-]/', '', $amountString);
        return floatval($cleaned);
    }

    private function calculateActualProgress($projectData)
    {
        // Simple calculation based on expense vs allocation
        if ($projectData['allocation_amount'] > 0) {
            $progress = ($projectData['expense_amount'] / $projectData['allocation_amount']) * 100;
            return min(100, max(0, round($progress)));
        }
        return 0;
    }

    private function getOrCreateMinistry()
    {
        return Ministry::firstOrCreate(
            ['name' => 'Ministry of Information Technology and Telecommunication'],
            ['isActive' => true]
        );
    }

    private function getOrCreateDepartment($name, $ministryId)
    {
        return Department::firstOrCreate(
            ['name' => $name, 'ministry_id' => $ministryId],
            ['isActive' => true]
        );
    }

    private function getOrCreateApprovingAuthority($name)
    {
        return ApprovingAuthority::firstOrCreate(
            ['name' => $name],
            ['isActive' => true]
        );
    }

    private function getOrCreateFinancialYear($name)
    {
        return FinancialYear::firstOrCreate(
            ['name' => $name],
            ['isActive' => true]
        );
    }

    private function getOrCreateStatus($name)
    {
        return Status::firstOrCreate(
            ['name' => $name]
        );
    }
}
