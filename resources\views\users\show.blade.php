@extends('layouts.app')

@section('title', 'User Details')

@section('content')
<div class="py-10 px-6">
    <div class="max-w-2xl mx-auto bg-white rounded-xl shadow p-5 sm:p-12 sm:px-16">
        <!-- Breadcrumb -->
        <div class="flex items-center gap-2 mb-6 text-sm text-gray-600">
            <a href="{{ route('users.index') }}" class="hover:text-blue-600">User Management</a>
            <span>></span>
            <span class="text-gray-900">{{ $user->name }}</span>
        </div>

        <!-- Header -->
        <h2 class="text-xl sm:text-2xl font-semibold text-center text-gray-900 mb-8">
            User Details
        </h2>

        <!-- User Information -->
        <div class="space-y-6">
            <!-- Name -->
            <div class="border-b border-gray-200 pb-4">
                <label class="block text-sm font-semibold text-gray-700 mb-2">Full Name</label>
                <p class="text-gray-900">{{ $user->name }}</p>
            </div>

            <!-- Email -->
            <div class="border-b border-gray-200 pb-4">
                <label class="block text-sm font-semibold text-gray-700 mb-2">Email Address</label>
                <p class="text-gray-900">{{ $user->email }}</p>
            </div>

            <!-- Role -->
            <div class="border-b border-gray-200 pb-4">
                <label class="block text-sm font-semibold text-gray-700 mb-2">Role</label>
                <div class="flex flex-wrap gap-2">
                    @foreach($user->roles as $role)
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                            @if($role->name === 'super admin') bg-purple-100 text-purple-800
                            @elseif($role->name === 'admin') bg-blue-100 text-blue-800
                            @elseif($role->name === 'PD') bg-green-100 text-green-800
                            @else bg-gray-100 text-gray-800 @endif">
                            {{ ucfirst($role->name) }}
                        </span>
                    @endforeach
                </div>
            </div>

            <!-- Ministry -->
            <div class="border-b border-gray-200 pb-4">
                <label class="block text-sm font-semibold text-gray-700 mb-2">Ministry</label>
                <p class="text-gray-900">
                    @if($user->userMinistries->first())
                        {{ $user->userMinistries->first()->ministry->name ?? 'N/A' }}
                    @else
                        Not Assigned
                    @endif
                </p>
            </div>

            <!-- Department -->
            <div class="border-b border-gray-200 pb-4">
                <label class="block text-sm font-semibold text-gray-700 mb-2">Department</label>
                <p class="text-gray-900">
                    @if($user->userMinistries->first() && $user->userMinistries->first()->department)
                        {{ $user->userMinistries->first()->department->name }}
                    @else
                        @if($user->hasRole('admin'))
                            All Departments (Ministry Admin)
                        @else
                            Not Assigned
                        @endif
                    @endif
                </p>
            </div>

            <!-- Status -->
            <div class="border-b border-gray-200 pb-4">
                <label class="block text-sm font-semibold text-gray-700 mb-2">Status</label>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                    @if($user->userMinistries->first() && $user->userMinistries->first()->is_active) 
                        bg-green-100 text-green-800
                    @else 
                        bg-red-100 text-red-800
                    @endif">
                    @if($user->userMinistries->first() && $user->userMinistries->first()->is_active)
                        Active
                    @else
                        Inactive
                    @endif
                </span>
            </div>

            <!-- Email Verification -->
            <div class="border-b border-gray-200 pb-4">
                <label class="block text-sm font-semibold text-gray-700 mb-2">Email Verification</label>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                    @if($user->email_verified_at) 
                        bg-green-100 text-green-800
                    @else 
                        bg-yellow-100 text-yellow-800
                    @endif">
                    @if($user->email_verified_at)
                        Verified ({{ $user->email_verified_at->format('M d, Y') }})
                    @else
                        Not Verified
                    @endif
                </span>
            </div>

            <!-- Created Date -->
            <div class="border-b border-gray-200 pb-4">
                <label class="block text-sm font-semibold text-gray-700 mb-2">Created Date</label>
                <p class="text-gray-900">{{ $user->created_at->format('F d, Y \a\t g:i A') }}</p>
            </div>

            <!-- Last Updated -->
            <div class="pb-4">
                <label class="block text-sm font-semibold text-gray-700 mb-2">Last Updated</label>
                <p class="text-gray-900">{{ $user->updated_at->format('F d, Y \a\t g:i A') }}</p>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row justify-center gap-4 mt-8">
            <a href="{{ route('users.index') }}"
               class="w-full sm:w-36 text-black text-sm font-medium rounded-full px-4 py-2.5 hover:brightness-105 text-center"
               style="background-color: #D6DCE4;">
                Back to List
            </a>
            
            @php
                $canEdit = false;
                $currentUser = auth()->user();
                
                if ($currentUser->hasRole('super admin') && $user->hasRole('admin')) {
                    $canEdit = true;
                } elseif ($currentUser->hasRole('admin') && $user->hasRole('PD')) {
                    $currentUserMinistry = $currentUser->userMinistries()->first();
                    $userMinistry = $user->userMinistries()->first();
                    
                    if ($currentUserMinistry && $userMinistry && 
                        $currentUserMinistry->ministry_id === $userMinistry->ministry_id) {
                        $canEdit = true;
                    }
                }
            @endphp
            
            @if($canEdit)
                <a href="{{ route('users.edit', $user) }}"
                   class="w-full sm:w-36 text-white text-sm font-medium rounded-full px-4 py-2.5 hover:brightness-110 text-center"
                   style="background-color: #1A92AA;">
                    Edit User
                </a>
            @endif
        </div>
    </div>
</div>
@endsection
