<?php

namespace App\Http\Controllers\PSDP;

use App\Http\Controllers\Controller;
use App\Models\PSDP\Project;
use App\Models\PSDP\Revision;
use App\Models\PSDP\ApprovingAuthority;
use App\Models\UserMinistry;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class RevisionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = auth()->user();
        $revisionsQuery = Revision::with(['project', 'project.department', 'approvingAuthority', 'addedByUser']);

        // Apply role-based filtering
        if ($user->hasRole('super admin')) {
            // Super admin sees all revisions
        } elseif ($user->hasRole('admin')) {
            // Ministry admin sees revisions from their ministry departments
            $userMinistries = UserMinistry::where('user_id', $user->id)->pluck('ministry_id');
            $revisionsQuery->whereHas('project.department', function($query) use ($userMinistries) {
                $query->whereIn('ministry_id', $userMinistries);
            });
        } elseif ($user->hasRole('PD')) {
            // PD sees only revisions for projects they added
            $revisionsQuery->whereHas('project', function($query) use ($user) {
                $query->where('added_by_user_id', $user->id);
            });
        }

        $revisions = $revisionsQuery->orderBy('created_at', 'desc')->paginate(10);

        return view('psdp.pd_projects.revisions.index', compact('revisions'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request, $pd_project = null)
    {
        // Handle both nested route and query parameter
        if ($pd_project) {
            $project = Project::findOrFail($pd_project);
        } else {
            $projectId = $request->get('project_id');
            $project = Project::findOrFail($projectId);
        }

        $approvingAuthorities = ApprovingAuthority::all();

        return view('psdp.pd_projects.revisions.create', compact('project', 'approvingAuthorities'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'project_id' => 'required|exists:projects,id',
            'authority_approved_date' => 'nullable|date',
            'approving_authority_id' => 'required|exists:approving_authorities,id',
            'admin_approval_date' => 'nullable|date',
            'revised_date' => 'required|date',
            'lc_amount' => 'nullable|numeric|min:0',
            'fc_amount' => 'nullable|numeric|min:0',
            'additional_cost' => 'nullable|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            // Create the revision (store amounts in millions)
            $revision = Revision::create([
                'project_id' => $request->project_id,
                'authority_approved_date' => $request->authority_approved_date,
                'approving_authority_id' => $request->approving_authority_id,
                'admin_approval_date' => $request->admin_approval_date,
                'revised_date' => $request->revised_date,
                'lc_amount' => $request->lc_amount ?? 0,
                'fc_amount' => $request->fc_amount ?? 0,
                'additional_cost' => $request->additional_cost ?? 0,
                'added_by_user_id' => auth()->id(),
            ]);

            // Update project with revision data (amounts already in millions)
            $project = Project::findOrFail($request->project_id);
            $project->update([
                'last_revised_date' => $request->revised_date,
                'grand_amount' => ($project->grand_amount ?? $project->total_cost) + ($request->additional_cost ?? 0),
                'is_revised' => true,
                'no_of_revision' => $project->no_of_revision + 1,
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Revision created successfully!',
                'redirect' => route('psdp.pd-projects.show', $project->id)
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Error creating revision: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show($pd_project, $revision)
    {
        $revision = Revision::with(['project', 'approvingAuthority', 'addedByUser'])->findOrFail($revision);
        return view('psdp.pd_projects.revisions.show', compact('revision'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($pd_project, $revision)
    {
        $revision = Revision::findOrFail($revision);

        // Database already stores amounts in millions, no conversion needed
        $revision->lc_amount_millions = $revision->lc_amount;
        $revision->fc_amount_millions = $revision->fc_amount;
        $revision->additional_cost_millions = $revision->additional_cost;

        $approvingAuthorities = ApprovingAuthority::all();
        return view('psdp.pd_projects.revisions.edit', compact('revision', 'approvingAuthorities'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $pd_project, $revision)
    {
        $revisionModel = Revision::findOrFail($revision);

        $validator = Validator::make($request->all(), [
            'authority_approved_date' => 'nullable|date',
            'approving_authority_id' => 'required|exists:approving_authorities,id',
            'admin_approval_date' => 'nullable|date',
            'revised_date' => 'required|date',
            'lc_amount' => 'nullable|numeric|min:0',
            'fc_amount' => 'nullable|numeric|min:0',
            'additional_cost' => 'nullable|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            $oldAdditionalCost = $revisionModel->additional_cost ?? 0;
            $newAdditionalCost = $request->additional_cost ?? 0; // Store amounts in millions
            $costDifference = $newAdditionalCost - $oldAdditionalCost;

            // Update the revision (store amounts in millions)
            $revisionModel->update([
                'authority_approved_date' => $request->authority_approved_date,
                'approving_authority_id' => $request->approving_authority_id,
                'admin_approval_date' => $request->admin_approval_date,
                'revised_date' => $request->revised_date,
                'lc_amount' => $request->lc_amount ?? 0,
                'fc_amount' => $request->fc_amount ?? 0,
                'additional_cost' => $newAdditionalCost,
            ]);

            // Update project's grand_amount if additional_cost changed
            if ($costDifference != 0) {
                $project = $revisionModel->project;
                $project->update([
                    'grand_amount' => $project->grand_amount + $costDifference,
                ]);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Revision updated successfully!',
                'redirect' => route('psdp.pd-projects.revisions.show', [$pd_project, $revision])
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Error updating revision: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($pd_project, $revision)
    {
        try {
            DB::beginTransaction();

            $revisionModel = Revision::findOrFail($revision);
            $project = $revisionModel->project;

            // Subtract the additional cost from grand_amount
            $project->update([
                'grand_amount' => $project->grand_amount - ($revisionModel->additional_cost ?? 0),
                'no_of_revision' => max(0, $project->no_of_revision - 1),
            ]);

            // If this was the last revision, update is_revised status
            if ($project->revisions()->count() <= 1) {
                $project->update(['is_revised' => false]);
            }

            $revisionModel->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Revision deleted successfully!'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Error deleting revision: ' . $e->getMessage()
            ], 500);
        }
    }
}
