@extends('layouts.app')

@section('title', 'Project Revisions')

@section('content')
<div class="py-10 px-6">
    <div class="max-w-full mx-auto bg-white rounded-xl shadow p-5 sm:p-12 sm:px-16">
        <!-- Breadcrumb -->
        <div class="flex items-center gap-2 mb-6 text-sm text-gray-600">
            <a href="{{ route('psdp.pd-dashboard') }}" class="hover:text-blue-600">PSDP Projects</a>
            <span>></span>
            <span class="text-gray-900">Project Revisions</span>
        </div>

        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <h2 class="text-xl sm:text-2xl font-semibold text-gray-900">
                Project Revisions
            </h2>
        </div>

        <!-- Revisions Table -->
        @if($revisions->count() > 0)
        <div class="overflow-x-auto">
            <table class="w-full border-collapse border border-gray-300 rounded-lg">
                <thead>
                    <tr class="bg-gray-50">
                        <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Project</th>
                        <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Revised Date</th>
                        <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Approving Authority</th>
                        <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Additional Cost</th>
                        <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Added By</th>
                        <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($revisions as $revision)
                    <tr class="hover:bg-gray-50">
                        <td class="border border-gray-300 px-4 py-3">
                            <div class="text-sm font-medium text-gray-900">{{ $revision->project->name }}</div>
                            <div class="text-xs text-gray-500">{{ $revision->project->psdp_id }}</div>
                        </td>
                        <td class="border border-gray-300 px-4 py-3 text-sm text-gray-700">
                            {{ $revision->revised_date->format('M d, Y') }}
                        </td>
                        <td class="border border-gray-300 px-4 py-3 text-sm text-gray-700">
                            {{ $revision->approvingAuthority->name }}
                        </td>
                        <td class="border border-gray-300 px-4 py-3 text-sm text-gray-700">
                            PKR {{ number_format($revision->additional_cost, 2) }}
                        </td>
                        <td class="border border-gray-300 px-4 py-3 text-sm text-gray-700">
                            {{ $revision->addedByUser->name ?? 'N/A' }}
                        </td>
                        <td class="border border-gray-300 px-4 py-3">
                            <div class="flex space-x-2">
                                <a href="{{ route('psdp.pd-projects.revisions.show', [$revision->project_id, $revision->id]) }}"
                                   class="text-blue-600 hover:text-blue-800 text-xs font-medium">
                                    View
                                </a>
                                <a href="{{ route('psdp.pd-projects.revisions.edit', [$revision->project_id, $revision->id]) }}"
                                   class="text-green-600 hover:text-green-800 text-xs font-medium">
                                    Edit
                                </a>
                                <button onclick="deleteRevision({{ $revision->project_id }}, {{ $revision->id }})"
                                        class="text-red-600 hover:text-red-800 text-xs font-medium">
                                    Delete
                                </button>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="mt-6">
            {{ $revisions->links() }}
        </div>
        @else
        <div class="text-center py-12">
            <div class="text-gray-500 text-lg mb-4">No revisions found</div>
            <p class="text-gray-400">Project revisions will appear here when they are created.</p>
        </div>
        @endif
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
function deleteRevision(projectId, revisionId) {
    if (confirm('Are you sure you want to delete this revision? This action cannot be undone.')) {
        $.ajax({
            url: `/psdp/pd-projects/${projectId}/revisions/${revisionId}`,
            method: 'DELETE',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    alert('Revision deleted successfully!');
                    location.reload();
                }
            },
            error: function(xhr) {
                alert('Error deleting revision. Please try again.');
            }
        });
    }
}
</script>
@endsection
