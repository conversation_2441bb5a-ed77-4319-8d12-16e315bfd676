@extends('layouts.app')

@section('title', 'Project Revisions')

@section('content')
<div class="py-10 px-6">
    <div class="max-w-full mx-auto bg-white rounded-xl shadow p-5 sm:p-12 sm:px-16">
        <!-- Breadcrumb -->
        <div class="flex items-center gap-2 mb-6 text-sm text-gray-600">
            <a href="{{ route('psdp.pd-dashboard') }}" class="hover:text-blue-600">PSDP Projects</a>
            <span>></span>
            <span class="text-gray-900">Project Revisions</span>
        </div>

        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <h2 class="text-xl sm:text-2xl font-semibold text-gray-900">
                Project Revisions
            </h2>
        </div>

        <!-- Revisions Table -->
        @if($revisions->count() > 0)
        <div class="overflow-x-auto">
            <table class="w-full border-collapse border border-gray-300 rounded-lg">
                <thead>
                    <tr class="bg-gray-50">
                        <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Project</th>
                        <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Revised Date</th>
                        <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Approving Authority</th>
                        <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Additional Cost</th>
                        <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Added By</th>
                        <th class="border border-gray-300 px-4 py-3 text-left text-sm font-semibold text-gray-700">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($revisions as $revision)
                    <tr class="hover:bg-gray-50">
                        <td class="border border-gray-300 px-4 py-3">
                            <div class="text-sm font-medium text-gray-900">{{ $revision->project->name }}</div>
                            <div class="text-xs text-gray-500">{{ $revision->project->psdp_id }}</div>
                        </td>
                        <td class="border border-gray-300 px-4 py-3 text-sm text-gray-700">
                            {{ $revision->revised_date->format('M d, Y') }}
                        </td>
                        <td class="border border-gray-300 px-4 py-3 text-sm text-gray-700">
                            {{ $revision->approvingAuthority->name }}
                        </td>
                        <td class="border border-gray-300 px-4 py-3 text-sm text-gray-700">
                            {{ number_format($revision->additional_cost / 1000000, 3) }} Million PKR
                        </td>
                        <td class="border border-gray-300 px-4 py-3 text-sm text-gray-700">
                            {{ $revision->addedByUser->name ?? 'N/A' }}
                        </td>
                        <td class="border border-gray-300 px-4 py-3">
                            <div class="flex space-x-3">
                                <a href="{{ route('psdp.pd-projects.revisions.show', [$revision->project_id, $revision->id]) }}"
                                   class="text-blue-600 hover:text-blue-800 p-1 rounded-full hover:bg-blue-50"
                                   title="View Revision">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                    </svg>
                                </a>
                                <a href="{{ route('psdp.pd-projects.revisions.edit', [$revision->project_id, $revision->id]) }}"
                                   class="text-green-600 hover:text-green-800 p-1 rounded-full hover:bg-green-50"
                                   title="Edit Revision">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                    </svg>
                                </a>
                                <button onclick="deleteRevision({{ $revision->project_id }}, {{ $revision->id }})"
                                        class="text-red-600 hover:text-red-800 p-1 rounded-full hover:bg-red-50"
                                        title="Delete Revision">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                    </svg>
                                </button>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="mt-6">
            {{ $revisions->links() }}
        </div>
        @else
        <div class="text-center py-12">
            <div class="text-gray-500 text-lg mb-4">No revisions found</div>
            <p class="text-gray-400">Project revisions will appear here when they are created.</p>
        </div>
        @endif
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
function deleteRevision(projectId, revisionId) {
    if (confirm('Are you sure you want to delete this revision? This action cannot be undone.')) {
        $.ajax({
            url: `/psdp/pd-projects/${projectId}/revisions/${revisionId}`,
            method: 'DELETE',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    alert('Revision deleted successfully!');
                    location.reload();
                }
            },
            error: function(xhr) {
                alert('Error deleting revision. Please try again.');
            }
        });
    }
}
</script>
@endsection
