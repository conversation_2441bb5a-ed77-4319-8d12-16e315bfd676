<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('projects', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignId('ministry_id')->nullable()->constrained('minestries')->onDelete('cascade');
            $table->foreignId('department_id')->constrained('departments')->onDelete('cascade');
            $table->string('psdp_no');
            $table->string('psdp_id');
            $table->foreignId('approving_authority_id')->constrained('approving_authorities')->onDelete('cascade');
            $table->date('authority_approved_date')->nullable();
            $table->date('admin_approval_date')->nullable();
            $table->date('completion_date_pc1')->nullable();
            $table->date('likely_completion_date')->nullable();
            $table->decimal('total_cost', 15, 2)->nullable()->comment('Total project cost');
            $table->decimal('grand_amount', 15, 2)->nullable()->comment('Grand project cost');
            $table->decimal('lc_amount', 15, 2)->nullable()->comment('Local Currency amount');
            $table->decimal('fc_amount', 15, 2)->nullable()->comment('Foreign Currency amount');
            $table->boolean('is_revised')->default(false);
            $table->integer('no_of_revision')->default(0);
            $table->date('last_revised_date')->nullable();
            $table->foreignId('added_by_user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            $table->date('updated_at_date')->nullable();
            $table->date('deleted_at_date')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('projects');
    }
};
