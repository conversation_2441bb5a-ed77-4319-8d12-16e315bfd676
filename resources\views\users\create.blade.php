@extends('layouts.app')

@section('title', 'Create User')

@section('content')
<div class="py-10 px-6">
    <div class="max-w-2xl mx-auto bg-white rounded-xl shadow p-5 sm:p-12 sm:px-16">
        <!-- Breadcrumb -->
        <div class="flex items-center gap-2 mb-6 text-sm text-gray-600">
            <a href="{{ route('users.index') }}" class="hover:text-blue-600">User Management</a>
            <span>></span>
            <span class="text-gray-900">Create User</span>
        </div>

        <!-- Header -->
        <h2 class="text-xl sm:text-2xl font-semibold text-center text-gray-900 mb-8">
            @if(auth()->user()->hasRole('super admin'))
                Create Ministry User
            @else
                Create PD User
            @endif
        </h2>

        <!-- Success/Error Messages -->
        @if(session('success'))
            <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                {{ session('success') }}
            </div>
        @endif

        @if(session('error'))
            <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                {{ session('error') }}
            </div>
        @endif

        <!-- Form -->
        <form method="POST" action="{{ route('users.store') }}">
            @csrf

            <!-- Name -->
            <div class="relative mt-6 w-full mx-auto mb-6">
                <input type="text" name="name" id="name" required value="{{ old('name') }}"
                       class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0 @error('name') border-red-500 @enderror"
                       placeholder="Enter full name" />
                <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                    Full Name*
                </label>
                @error('name')
                    <span class="text-red-500 text-xs mt-1">{{ $message }}</span>
                @enderror
            </div>

            <!-- Email -->
            <div class="relative mt-6 w-full mx-auto mb-6">
                <input type="email" name="email" id="email" required value="{{ old('email') }}"
                       class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0 @error('email') border-red-500 @enderror"
                       placeholder="Enter email address" />
                <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                    Email Address*
                </label>
                @error('email')
                    <span class="text-red-500 text-xs mt-1">{{ $message }}</span>
                @enderror
            </div>

            <!-- Password -->
            <div class="relative mt-6 w-full mx-auto mb-6">
                <input type="password" name="password" id="password" required
                       class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0 @error('password') border-red-500 @enderror"
                       placeholder="Enter password" />
                <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                    Password*
                </label>
                @error('password')
                    <span class="text-red-500 text-xs mt-1">{{ $message }}</span>
                @enderror
            </div>

            <!-- Confirm Password -->
            <div class="relative mt-6 w-full mx-auto mb-6">
                <input type="password" name="password_confirmation" id="password_confirmation" required
                       class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0"
                       placeholder="Confirm password" />
                <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                    Confirm Password*
                </label>
            </div>

            <!-- Role (Hidden for ministry users) -->
            @if(auth()->user()->hasRole('super admin'))
                <div class="relative mt-6 w-full mx-auto mb-6">
                    <select name="role" id="role" required
                            class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0 appearance-none @error('role') border-red-500 @enderror">
                        <option value="">Select role</option>
                        @foreach($roles as $role)
                            <option value="{{ $role->name }}" {{ old('role') == $role->name ? 'selected' : '' }}>
                                {{ ucfirst($role->name) }}
                            </option>
                        @endforeach
                    </select>
                    <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                        Role*
                    </label>
                    <div class="pointer-events-none absolute inset-y-0 right-4 flex items-center text-gray-500">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                            <path d="M19 9l-7 7-7-7" />
                        </svg>
                    </div>
                    @error('role')
                        <span class="text-red-500 text-xs mt-1">{{ $message }}</span>
                    @enderror
                </div>
            @else
                <input type="hidden" name="role" value="PD">
            @endif

            <!-- Ministry -->
            @if(auth()->user()->hasRole('super admin'))
                <div class="relative mt-6 w-full mx-auto mb-6">
                    <select name="ministry_id" id="ministry_id" required
                            class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0 appearance-none @error('ministry_id') border-red-500 @enderror">
                        <option value="">Select ministry</option>
                        @foreach($ministries as $ministry)
                            <option value="{{ $ministry->id }}" {{ old('ministry_id') == $ministry->id ? 'selected' : '' }}>
                                {{ $ministry->name }}
                            </option>
                        @endforeach
                    </select>
                    <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                        Ministry*
                    </label>
                    <div class="pointer-events-none absolute inset-y-0 right-4 flex items-center text-gray-500">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                            <path d="M19 9l-7 7-7-7" />
                        </svg>
                    </div>
                    @error('ministry_id')
                        <span class="text-red-500 text-xs mt-1">{{ $message }}</span>
                    @enderror
                </div>
            @else
                @php
                    $userMinistry = auth()->user()->userMinistries()->with('ministry')->first();
                @endphp
                <input type="hidden" name="ministry_id" value="{{ $userMinistry->ministry_id }}">
                
                <!-- Show ministry name (read-only) -->
                <div class="relative mt-6 w-full mx-auto mb-6">
                    <input type="text" value="{{ $userMinistry->ministry->name ?? 'No Ministry Assigned' }}" readonly
                           class="block w-full text-sm text-gray-500 bg-gray-100 rounded-full border py-3 pl-4 pr-4 outline-0" />
                    <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                        Ministry
                    </label>
                </div>
            @endif

            <!-- Department (Only for PD users) -->
            @if(auth()->user()->hasRole('admin'))
                <div class="relative mt-6 w-full mx-auto mb-6">
                    <select name="department_id" id="department_id" required
                            class="block w-full text-sm text-gray-900 bg-transparent rounded-full border py-3 pl-4 pr-4 outline-0 appearance-none @error('department_id') border-red-500 @enderror">
                        <option value="">Select department</option>
                        @foreach($departments as $department)
                            <option value="{{ $department->id }}" {{ old('department_id') == $department->id ? 'selected' : '' }}>
                                {{ $department->name }}
                            </option>
                        @endforeach
                    </select>
                    <label class="absolute text-[16px] font-semibold text-[#0D163A] duration-300 transform scale-75 top-3 z-10 origin-[0] bg-white px-2 focus:px-2 focus:text-blue-600 placeholder-shown:scale-100 -translate-y-1/2 scale-75 -translate-y-6 start-4">
                        Department*
                    </label>
                    <div class="pointer-events-none absolute inset-y-0 right-4 flex items-center text-gray-500">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                            <path d="M19 9l-7 7-7-7" />
                        </svg>
                    </div>
                    @error('department_id')
                        <span class="text-red-500 text-xs mt-1">{{ $message }}</span>
                    @enderror
                </div>
            @endif

            <!-- Buttons -->
            <div class="flex flex-col sm:flex-row justify-center gap-4 mt-8">
                <a href="{{ route('users.index') }}"
                   class="w-full sm:w-36 text-black text-sm font-medium rounded-full px-4 py-2.5 hover:brightness-105 text-center"
                   style="background-color: #D6DCE4;">
                    Cancel
                </a>
                <button type="submit"
                        class="w-full sm:w-36 text-white text-sm font-medium rounded-full px-4 py-2.5 hover:brightness-110"
                        style="background-color: #1A92AA;">
                    Create User
                </button>
            </div>
        </form>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
$(document).ready(function() {
    // For super admin: Load departments when ministry changes
    @if(auth()->user()->hasRole('super admin'))
    $('#ministry_id').change(function() {
        const ministryId = $(this).val();
        const departmentSelect = $('#department_id');
        
        // Clear department dropdown
        departmentSelect.html('<option value="">Select department</option>');
        
        if (ministryId) {
            $.get(`/users/departments/${ministryId}`)
                .done(function(departments) {
                    departments.forEach(department => {
                        departmentSelect.append(`<option value="${department.id}">${department.name}</option>`);
                    });
                })
                .fail(function() {
                    console.error('Failed to load departments');
                });
        }
    });
    
    // Show/hide department field based on role
    $('#role').change(function() {
        const role = $(this).val();
        const departmentField = $('#department_id').closest('.relative');
        
        if (role === 'PD') {
            departmentField.show();
        } else {
            departmentField.hide();
        }
    });
    
    // Initial state
    $('#role').trigger('change');
    @endif
});
</script>
@endsection
