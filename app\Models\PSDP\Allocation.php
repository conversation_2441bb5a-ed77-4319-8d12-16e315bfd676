<?php

namespace App\Models\PSDP;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Allocation extends Model
{
    protected $fillable = [
        'project_id',
        'financial_year_id',
        'expense_amount',
        'allocation_amount',
        'release_amount',
        'surrender_amount',
        'is_active'
    ];

    protected $casts = [
        'expense_amount' => 'decimal:2',
        'allocation_amount' => 'decimal:2',
        'release_amount' => 'decimal:2',
        'surrender_amount' => 'decimal:2',
        'is_active' => 'boolean',
        'deleted_at' => 'datetime'
    ];

    /**
     * Get the project that owns the allocation.
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get the financial year that owns the allocation.
     */
    public function financialYear(): BelongsTo
    {
        return $this->belongsTo(FinancialYear::class);
    }

    /**
     * Get the quarters for the allocation.
     */
    public function quarters(): HasMany
    {
        return $this->hasMany(Quarter::class);
    }
}
