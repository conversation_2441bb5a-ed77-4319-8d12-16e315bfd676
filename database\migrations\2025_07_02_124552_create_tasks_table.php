<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tasks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained('projects')->onDelete('cascade');
            $table->foreignId('deliverable_id')->constrained('deliverables')->onDelete('cascade');
            $table->string('code');
            $table->text('description');
            $table->date('start_date');
            $table->date('end_date');
            $table->decimal('planned_complete', 5, 2)->default(0);
            $table->decimal('actual_complete', 5, 2)->default(0);
            $table->foreignId('status_id')->constrained('statuses')->onDelete('cascade');
            $table->text('remarks')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tasks');
    }
};
