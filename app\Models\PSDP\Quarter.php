<?php

namespace App\Models\PSDP;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Quarter extends Model
{
    protected $fillable = [
        'project_id',
        'allocation_id',
        'quarter',
        'release_amount',
        'expense_amount',
        'is_active'
    ];

    protected $casts = [
        'release_amount' => 'decimal:2',
        'expense_amount' => 'decimal:2',
        'is_active' => 'boolean'
    ];

    /**
     * Get the project that owns the quarter.
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get the allocation that owns the quarter.
     */
    public function allocation(): BelongsTo
    {
        return $this->belongsTo(Allocation::class);
    }
}
