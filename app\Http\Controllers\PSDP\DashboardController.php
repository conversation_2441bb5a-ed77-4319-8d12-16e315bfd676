<?php

namespace App\Http\Controllers\PSDP;

use App\Http\Controllers\Controller;
use App\Models\PSDP\Project;
use App\Models\PSDP\Allocation;
use App\Models\PSDP\Quarter;
use App\Models\PSDP\FinancialYear;
use App\Models\UserMinistry;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function __construct()
    {
        // Middleware will be handled in routes or via middleware groups
    }

    public function index(Request $request)
    {
        $user = auth()->user();
        $projectsQuery = Project::query();

        // Get current financial year (2025-2026)
        $currentFinancialYear = FinancialYear::where('name', '2025-2026')->first();
        $defaultFYId = $currentFinancialYear ? $currentFinancialYear->id : 16; // Fallback to ID 16 for 2025-2026
        $selectedFinancialYearId = $request->get('financial_year_id', $defaultFYId);

        // Get all financial years for dropdown
        $financialYears = FinancialYear::where('is_active', true)->orderBy('name', 'desc')->get();
        $selectedFinancialYear = FinancialYear::find($selectedFinancialYearId);

        // Apply role-based filtering
        if ($user->hasRole('super admin')) {
            // Super admin sees all data - no filtering needed
        } elseif ($user->hasRole('admin')) {
            // Ministry admin sees data from their ministry and all its departments
            $userMinistry = $user->userMinistries()->first();
            if ($userMinistry) {
                $projectsQuery->where('projects.ministry_id', $userMinistry->ministry_id);
            }
        } elseif ($user->hasRole('PD')) {
            // PD sees only their specific department data
            $userMinistry = $user->userMinistries()->first();
            if ($userMinistry && $userMinistry->department_id) {
                $projectsQuery->where('projects.department_id', $userMinistry->department_id);
            }
        }

        // Get budget statistics
        $budgetStats = $this->getBudgetStatistics($projectsQuery, $selectedFinancialYearId);

        // Get department-wise allocation data
        $departmentAllocations = $this->getDepartmentAllocations($projectsQuery, $selectedFinancialYearId);

        // Get quarterly budget vs expenditure data
        $quarterlyData = $this->getQuarterlyData($projectsQuery, $selectedFinancialYearId);

        // Get project-wise financial overview
        $projectFinancialData = $this->getProjectFinancialData($projectsQuery, $selectedFinancialYearId);

        return view('psdp.dashboard', compact(
            'budgetStats',
            'departmentAllocations',
            'quarterlyData',
            'projectFinancialData',
            'financialYears',
            'selectedFinancialYear'
        ));
    }

    public function pdDashboard(Request $request)
    {
        $user = auth()->user();
        $projectsQuery = Project::query();

        // Get current financial year (2025-2026)
        $currentFinancialYear = FinancialYear::where('name', '2025-2026')->first();
        $defaultFYId = $currentFinancialYear ? $currentFinancialYear->id : 16; // Fallback to ID 16 for 2025-2026
        $selectedFinancialYearId = $request->get('financial_year_id', $defaultFYId);

        // Get all financial years for dropdown
        $financialYears = FinancialYear::where('is_active', true)->orderBy('name', 'desc')->get();
        $selectedFinancialYear = FinancialYear::find($selectedFinancialYearId);

        // Apply role-based filtering (same as main dashboard)
        if ($user->hasRole('super admin')) {
            // Super admin sees all data - no filtering needed
        } elseif ($user->hasRole('admin')) {
            // Ministry admin sees data from their ministry and all its departments
            $userMinistry = $user->userMinistries()->first();
            if ($userMinistry) {
                $projectsQuery->where('projects.ministry_id', $userMinistry->ministry_id);
            }
        } elseif ($user->hasRole('PD')) {
            // PD sees only their specific department data
            $userMinistry = $user->userMinistries()->first();
            if ($userMinistry && $userMinistry->department_id) {
                $projectsQuery->where('projects.department_id', $userMinistry->department_id);
            }
        }

        $totalProjects = $projectsQuery->count();

        $projectsByStatus = $projectsQuery->clone()
            ->join('tasks', 'projects.id', '=', 'tasks.project_id')
            ->join('statuses', 'tasks.status_id', '=', 'statuses.id')
            ->selectRaw('statuses.name as status, COUNT(DISTINCT projects.id) as count')
            ->groupBy('statuses.name')
            ->get();

        $projectsByDepartment = $projectsQuery->clone()
            ->join('departments', 'projects.department_id', '=', 'departments.id')
            ->selectRaw('departments.name as department, COUNT(projects.id) as count')
            ->groupBy('departments.name')
            ->get();

        // Get budget statistics with financial year filtering
        $budgetStats = $this->getBudgetStatistics($projectsQuery, $selectedFinancialYearId);

        return view('psdp.pd_dashboard', compact(
            'totalProjects',
            'projectsByStatus',
            'projectsByDepartment',
            'budgetStats',
            'financialYears',
            'selectedFinancialYear'
        ));
    }

    private function getBudgetStatistics($projectsQuery, $financialYearId = null)
    {
        $projects = $projectsQuery->clone()->with(['allocations' => function($query) use ($financialYearId) {
            if ($financialYearId) {
                $query->where('financial_year_id', $financialYearId);
            }
        }])->get();

        $totalBudgetAllocation = $projects->sum('grand_amount') ?? $projects->sum('total_cost');
        $finalAllocation = $projects->sum(function($project) {
            return $project->allocations->sum('allocation_amount');
        });
        $totalReleases = $projects->sum(function($project) {
            return $project->allocations->sum('release_amount');
        });
        $expenditure = $projects->sum(function($project) {
            return $project->allocations->sum('expense_amount');
        });

        $finalAllocationPercentage = $totalBudgetAllocation > 0 ? ($finalAllocation / $totalBudgetAllocation) * 100 : 0;
        $releasesPercentage = $finalAllocation > 0 ? ($totalReleases / $finalAllocation) * 100 : 0;
        $expenditurePercentage = $totalReleases > 0 ? ($expenditure / $totalReleases) * 100 : 0;

        return [
            'total_budget_allocation' => $totalBudgetAllocation,
            'final_allocation' => $finalAllocation,
            'final_allocation_percentage' => round($finalAllocationPercentage, 1),
            'total_releases' => $totalReleases,
            'releases_percentage' => round($releasesPercentage, 1),
            'expenditure' => $expenditure,
            'expenditure_percentage' => round($expenditurePercentage, 1),
        ];
    }

    private function getDepartmentAllocations($projectsQuery, $financialYearId = null)
    {
        $query = $projectsQuery->clone()
            ->join('departments', 'projects.department_id', '=', 'departments.id')
            ->join('allocations', 'projects.id', '=', 'allocations.project_id');

        if ($financialYearId) {
            $query->where('allocations.financial_year_id', $financialYearId);
        }

        return $query->selectRaw('departments.name as department, SUM(allocations.allocation_amount) as total_allocation')
            ->groupBy('departments.id', 'departments.name')
            ->orderByDesc('total_allocation')
            ->limit(5)
            ->get();
    }

    private function getQuarterlyData($projectsQuery, $financialYearId = null)
    {
        $projectIds = $projectsQuery->clone()->pluck('id');

        $query = Quarter::whereHas('allocation', function($query) use ($projectIds, $financialYearId) {
            $query->whereIn('project_id', $projectIds);
            if ($financialYearId) {
                $query->where('financial_year_id', $financialYearId);
            }
        });

        return $query->selectRaw('
                CASE
                    WHEN quarter = "Q1" THEN 1
                    WHEN quarter = "Q2" THEN 2
                    WHEN quarter = "Q3" THEN 3
                    WHEN quarter = "Q4" THEN 4
                END as quarter_num,
                quarter,
                SUM(release_amount) as budget,
                SUM(expense_amount) as expenditure
            ')
            ->groupBy('quarter', 'quarter_num')
            ->orderBy('quarter_num')
            ->get();
    }

    private function getProjectFinancialData($projectsQuery, $financialYearId = null)
    {
        return $projectsQuery->clone()
            ->with(['allocations' => function($query) use ($financialYearId) {
                if ($financialYearId) {
                    $query->where('financial_year_id', $financialYearId);
                }
            }])
            ->selectRaw('
                projects.id,
                projects.name,
                projects.total_cost,
                projects.grand_amount
            ')
            ->limit(9)
            ->get()
            ->map(function($project) {
                $totalAllocation = $project->allocations->sum('allocation_amount');
                $totalReleases = $project->allocations->sum('release_amount');
                $totalExpenditure = $project->allocations->sum('expense_amount');

                return [
                    'name' => $project->name,
                    'total_cost' => $project->total_cost ?? 0,
                    'allocation' => $totalAllocation,
                    'releases' => $totalReleases,
                    'expenditure' => $totalExpenditure,
                ];
            });
    }
}
