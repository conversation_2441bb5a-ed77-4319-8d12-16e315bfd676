<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions
        $permissions = [
            'view-projects',
            'create-projects',
            'edit-projects',
            'delete-projects',
            'manage-users',
            'manage-roles',
            'view-reports',
            'export-data',
            'manage-system-settings'
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles and assign permissions
        $superAdmin = Role::firstOrCreate(['name' => 'super admin']);
        $superAdmin->givePermissionTo(Permission::all());

        $admin = Role::firstOrCreate(['name' => 'admin']);
        $admin->givePermissionTo([
            'view-projects',
            'create-projects',
            'edit-projects',
            'delete-projects',
            'view-reports',
            'export-data'
        ]);

        $pd = Role::firstOrCreate(['name' => 'PD']);
        $pd->givePermissionTo([
            'view-projects',
            'create-projects',
            'edit-projects',
            'view-reports'
        ]);
    }
}
